package main

import (
	"context"
	"flag"
	"fmt"
	"log"
	"os"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/initializer"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/service/activity_cashback"
)

func main() {
	var configPath string
	flag.StringVar(&configPath, "config", "config.yaml", "Path to config file")
	flag.Parse()

	// Initialize configuration first
	global.GVA_VP = initializer.Viper(configPath)
	// Config is loaded automatically by Viper

	fmt.Println("Starting task reseeding...")

	// Check if task seeder is enabled BEFORE initializing database
	if !global.GVA_CONFIG.System.EnableTaskSeeder {
		fmt.Println("⚠️  Task seeder is disabled in configuration (ENABLE_TASK_SEEDER=false)")
		fmt.Println("   To enable task seeding, set ENABLE_TASK_SEEDER=true in your environment")
		fmt.Println("   This is a safety measure to prevent accidental seeding in production")
		os.Exit(1)
	}

	// Initialize database only after safety check passes
	global.GVA_LOG = initializer.Zap()
	global.GVA_DB = initializer.GormPgSql()

	if global.GVA_DB == nil {
		log.Fatal("Failed to initialize database connection")
	}

	// Create task seeder directly
	taskSeeder := activity_cashback.NewTaskSeeder()

	ctx := context.Background()

	// Reseed tasks
	if err := taskSeeder.SeedTasks(ctx); err != nil {
		log.Fatalf("Failed to seed tasks: %v", err)
	}
	fmt.Println("✓ Tasks reseeded successfully")

	fmt.Println("Task reseeding completed successfully!")
	os.Exit(0)
}
