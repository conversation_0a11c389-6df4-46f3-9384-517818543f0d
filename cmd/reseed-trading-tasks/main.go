package main

import (
	"context"
	"flag"
	"fmt"
	"log"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/initializer"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/service/activity_cashback"
)

func main() {
	var configPath string
	flag.StringVar(&configPath, "config", "config.yaml", "Path to config file")
	flag.Parse()

	// Initialize configuration first
	global.GVA_VP = initializer.Viper(configPath)

	fmt.Println("🔄 Starting trading tasks reseed...")

	// Check if task seeder is enabled BEFORE initializing database
	if !global.GVA_CONFIG.System.EnableTaskSeeder {
		fmt.Println("⚠️  Task seeder is disabled in configuration (ENABLE_TASK_SEEDER=false)")
		fmt.Println("   To enable task seeding, set ENABLE_TASK_SEEDER=true in your environment")
		fmt.Println("   This is a safety measure to prevent accidental seeding in production")
		log.Fatal("Task seeder is disabled")
	}

	// Initialize database only after safety check passes
	global.GVA_LOG = initializer.Zap()
	global.GVA_DB = initializer.GormPgSql()

	if global.GVA_DB == nil {
		log.Fatal("Failed to initialize database connection")
	}

	global.GVA_LOG.Info("🔄 Task seeder is enabled, proceeding with trading tasks reseed...")

	// Create task seeder
	seeder := activity_cashback.NewTaskSeeder()

	ctx := context.Background()

	// Reseed all tasks (this will create new trading tasks with identifiers)
	if err := seeder.SeedTasks(ctx); err != nil {
		log.Fatalf("❌ Failed to reseed tasks: %v", err)
	}

	global.GVA_LOG.Info("✅ Trading tasks reseed completed successfully!")
	global.GVA_LOG.Info("📋 New trading tasks with identifiers have been created:")
	global.GVA_LOG.Info("   - MEME_TRADE_DAILY (trading category)")
	global.GVA_LOG.Info("   - PERPETUAL_TRADE_DAILY (trading category)")
	global.GVA_LOG.Info("   - TRADING_POINTS (trading category)")
	global.GVA_LOG.Info("🚀 NATS events should now work correctly for MEME trades!")
}
