package graphql

import (
	// "gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/config"
	// "gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/graphql/resolvers"
)

// This file will not be regenerated automatically.
//
// It serves as dependency injection for your app, add any dependencies you require here.

type Resolver struct {
	InvitationService   *resolvers.InvitationResolver
	TransactionService  *resolvers.TransactionResolver
	DataOverviewService *resolvers.DataOverviewResolver
	RewardService       *resolvers.RewardResolver
	ClaimService        *resolvers.ClaimResolver
}

func NewRootResolver() *Resolver {
	return &Resolver{
		InvitationService:   resolvers.NewInvitationResolver(),
		TransactionService:  resolvers.NewTransactionResolver(),
		DataOverviewService: resolvers.NewDataOverviewResolver(),
		RewardService:       resolvers.NewRewardResolver(),
		ClaimService:        resolvers.NewClaimResolver(),
	}
}
