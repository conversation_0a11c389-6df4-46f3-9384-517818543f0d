# Admin Agent Referral Management Schema

# This file is intentionally minimal as all types are defined in the main schema.graphqls file
# to avoid duplication and conflicts during GraphQL generation.
type InfiniteAgentConfig {
  id: ID!
  userID: ID!
  commissionRateN: Float!
  Status: String!
  createdAt: Time!
  updatedAt: Time!
  user: User
}
enum StatusType {
  ACTIVE
  INACTIVE
}

input CreateInfiniteAgentConfigInput {
  userID: ID!
  commissionRateN: Float!
  status: StatusType!
}
type CreateInfiniteAgentConfigResponse {
  infiniteAgentConfig: InfiniteAgentConfig
  success: Boolean!
  message: String!
}
