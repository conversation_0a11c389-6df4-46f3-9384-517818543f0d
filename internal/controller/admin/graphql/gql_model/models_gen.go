// Code generated by github.com/99designs/gqlgen, DO NOT EDIT.

package gql_model

import (
	"bytes"
	"fmt"
	"io"
	"strconv"
	"time"
)

type ALlTaskCategoriesResponse struct {
	Data       []*TaskCategory `json:"data"`
	Total      int             `json:"total"`
	Page       int             `json:"page"`
	PageSize   int             `json:"pageSize"`
	TotalPages int             `json:"totalPages"`
}

type ALlTierBenefitsResponse struct {
	Data       []*TierBenefit `json:"data"`
	Total      int            `json:"total"`
	Page       int            `json:"page"`
	PageSize   int            `json:"pageSize"`
	TotalPages int            `json:"totalPages"`
}

type ActivityTask struct {
	ID                 string            `json:"id"`
	CategoryID         string            `json:"categoryId"`
	Category           *TaskCategory     `json:"category"`
	Name               *MultilingualName `json:"name"`
	Description        *string           `json:"description,omitempty"`
	Frequency          TaskFrequency     `json:"frequency"`
	TaskIdentifier     *TaskIdentifier   `json:"taskIdentifier,omitempty"`
	Points             int               `json:"points"`
	MaxCompletions     *int              `json:"maxCompletions,omitempty"`
	ResetPeriod        *string           `json:"resetPeriod,omitempty"`
	Conditions         *string           `json:"conditions,omitempty"`
	ActionTarget       *string           `json:"actionTarget,omitempty"`
	VerificationMethod *string           `json:"verificationMethod,omitempty"`
	ExternalLink       *string           `json:"externalLink,omitempty"`
	TaskIcon           *string           `json:"taskIcon,omitempty"`
	ButtonText         *string           `json:"buttonText,omitempty"`
	StartDate          *int64            `json:"startDate,omitempty"`
	EndDate            *int64            `json:"endDate,omitempty"`
	SortOrder          int               `json:"sortOrder"`
	IsActive           bool              `json:"isActive"`
	CreatedAt          time.Time         `json:"createdAt"`
	UpdatedAt          time.Time         `json:"updatedAt"`
}

type AdminInfiniteAgentConfig struct {
	ID                             string    `json:"id"`
	UserID                         string    `json:"userId"`
	User                           *User     `json:"user,omitempty"`
	CommissionRateN                float64   `json:"commissionRateN"`
	TotalNetFeeUsd                 float64   `json:"totalNetFeeUsd"`
	TotalStandardCommissionPaidUsd float64   `json:"totalStandardCommissionPaidUsd"`
	FinalCommissionAmountUsd       float64   `json:"finalCommissionAmountUsd"`
	MemeVolumeUsd                  float64   `json:"memeVolumeUsd"`
	MemePaidCommissionUsd          float64   `json:"memePaidCommissionUsd"`
	MemeNetFeeUsd                  float64   `json:"memeNetFeeUsd"`
	ContractTotalFeeUsd            float64   `json:"contractTotalFeeUsd"`
	ContractPaidCommissionUsd      float64   `json:"contractPaidCommissionUsd"`
	ContractNetFeeUsd              float64   `json:"contractNetFeeUsd"`
	Status                         string    `json:"status"`
	CreatedAt                      time.Time `json:"createdAt"`
	UpdatedAt                      time.Time `json:"updatedAt"`
}

type AdminInfiniteAgentsResponse struct {
	Agents     []*AdminInfiniteAgentConfig `json:"agents"`
	Total      int                         `json:"total"`
	Page       int                         `json:"page"`
	PageSize   int                         `json:"pageSize"`
	TotalPages int                         `json:"totalPages"`
}

type AdminStatsDateInput struct {
	StartDate time.Time `json:"startDate"`
	EndDate   time.Time `json:"endDate"`
}

type AdminStatsInput struct {
	StartDate time.Time `json:"startDate"`
	EndDate   time.Time `json:"endDate"`
}

type AdminTaskCompletionStats struct {
	TaskCompletions []*TaskCompletionStat `json:"taskCompletions"`
	StartDate       time.Time             `json:"startDate"`
	EndDate         time.Time             `json:"endDate"`
	TotalTasks      int                   `json:"totalTasks"`
}

type AdminTaskCompletionStatsResponse struct {
	Success bool                      `json:"success"`
	Message string                    `json:"message"`
	Data    *AdminTaskCompletionStats `json:"data,omitempty"`
}

type AdminTierDistributionResponse struct {
	Success bool                    `json:"success"`
	Message string                  `json:"message"`
	Data    []*TierDistributionStat `json:"data"`
}

type AdminUserActivityStats struct {
	DailyCompletions []*DailyCompletionStat `json:"dailyCompletions"`
	StartDate        time.Time              `json:"startDate"`
	EndDate          time.Time              `json:"endDate"`
}

type AdminUserActivityStatsResponse struct {
	Success bool                    `json:"success"`
	Message string                  `json:"message"`
	Data    *AdminUserActivityStats `json:"data,omitempty"`
}

type AgentLevel struct {
	ID                      int     `json:"id"`
	Name                    string  `json:"name"`
	MemeVolumeThreshold     float64 `json:"memeVolumeThreshold"`
	ContractVolumeThreshold float64 `json:"contractVolumeThreshold"`
	MemeFeeRate             float64 `json:"memeFeeRate"`
	TakerFeeRate            float64 `json:"takerFeeRate"`
	MakerFeeRate            float64 `json:"makerFeeRate"`
	DirectCommissionRate    float64 `json:"directCommissionRate"`
	IndirectCommissionRate  float64 `json:"indirectCommissionRate"`
	ExtendedCommissionRate  float64 `json:"extendedCommissionRate"`
	MemeFeeRebate           float64 `json:"memeFeeRebate"`
}

type AgentLevelWithStats struct {
	ID                      int     `json:"id"`
	Name                    string  `json:"name"`
	MemeVolumeThreshold     float64 `json:"memeVolumeThreshold"`
	ContractVolumeThreshold float64 `json:"contractVolumeThreshold"`
	MemeFeeRate             float64 `json:"memeFeeRate"`
	TakerFeeRate            float64 `json:"takerFeeRate"`
	MakerFeeRate            float64 `json:"makerFeeRate"`
	DirectCommissionRate    float64 `json:"directCommissionRate"`
	IndirectCommissionRate  float64 `json:"indirectCommissionRate"`
	ExtendedCommissionRate  float64 `json:"extendedCommissionRate"`
	MemeFeeRebate           float64 `json:"memeFeeRebate"`
	UserCount               int     `json:"userCount"`
	TotalVolumeUsd          float64 `json:"totalVolumeUsd"`
	TotalCommission         float64 `json:"totalCommission"`
}

type AgentReferralStats struct {
	TotalUsers          int     `json:"totalUsers"`
	TotalInfiniteAgents int     `json:"totalInfiniteAgents"`
	TotalVolumeUsd      float64 `json:"totalVolumeUsd"`
	TotalCommissionPaid float64 `json:"totalCommissionPaid"`
	ActiveReferralTrees int     `json:"activeReferralTrees"`
	AverageTreeSize     float64 `json:"averageTreeSize"`
}

type AllTaskCategoriesInput struct {
	Page      *int    `json:"page,omitempty"`
	PageSize  *int    `json:"pageSize,omitempty"`
	SortBy    *string `json:"sortBy,omitempty"`
	SortOrder *string `json:"sortOrder,omitempty"`
}

type AllTierBenefitsInput struct {
	Page      *int    `json:"page,omitempty"`
	PageSize  *int    `json:"pageSize,omitempty"`
	SortBy    *string `json:"sortBy,omitempty"`
	SortOrder *string `json:"sortOrder,omitempty"`
}

type CommissionDistributionStats struct {
	DirectCommission   float64 `json:"directCommission"`
	IndirectCommission float64 `json:"indirectCommission"`
	ExtendedCommission float64 `json:"extendedCommission"`
	InfiniteCommission float64 `json:"infiniteCommission"`
	TotalCommission    float64 `json:"totalCommission"`
}

type ConsecutiveCheckinMilestoneInput struct {
	Days     int                    `json:"days"`
	Points   int                    `json:"points"`
	Name     *MultilingualNameInput `json:"name,omitempty"`
	TaskIcon *string                `json:"taskIcon,omitempty"`
}

type CreateAccumulatedMEMETradingVolumeTaskInput struct {
	VolumeThreshold    float64                `json:"volumeThreshold"`
	Points             int                    `json:"points"`
	CategoryID         *string                `json:"categoryId,omitempty"`
	Name               *MultilingualNameInput `json:"name,omitempty"`
	Description        *string                `json:"description,omitempty"`
	MaxCompletions     *int                   `json:"maxCompletions,omitempty"`
	ActionTarget       *string                `json:"actionTarget,omitempty"`
	VerificationMethod *string                `json:"verificationMethod,omitempty"`
	ExternalLink       *string                `json:"externalLink,omitempty"`
	TaskIcon           *string                `json:"taskIcon,omitempty"`
	ButtonText         *string                `json:"buttonText,omitempty"`
	StartDate          *int64                 `json:"startDate,omitempty"`
	EndDate            *int64                 `json:"endDate,omitempty"`
	SortOrder          *int                   `json:"sortOrder,omitempty"`
}

type CreateAgentLevelInput struct {
	Name                    string  `json:"name"`
	MemeVolumeThreshold     float64 `json:"memeVolumeThreshold"`
	ContractVolumeThreshold float64 `json:"contractVolumeThreshold"`
	MemeFeeRate             float64 `json:"memeFeeRate"`
	TakerFeeRate            float64 `json:"takerFeeRate"`
	MakerFeeRate            float64 `json:"makerFeeRate"`
	DirectCommissionRate    float64 `json:"directCommissionRate"`
	IndirectCommissionRate  float64 `json:"indirectCommissionRate"`
	ExtendedCommissionRate  float64 `json:"extendedCommissionRate"`
	MemeFeeRebate           float64 `json:"memeFeeRebate"`
}

type CreateAgentLevelResponse struct {
	Level   *AgentLevel `json:"level,omitempty"`
	Success bool        `json:"success"`
	Message string      `json:"message"`
}

type CreateConsecutiveCheckinTaskInput struct {
	CategoryID         string                              `json:"categoryId"`
	Name               *MultilingualNameInput              `json:"name"`
	Description        *string                             `json:"description,omitempty"`
	Milestones         []*ConsecutiveCheckinMilestoneInput `json:"milestones"`
	ActionTarget       *string                             `json:"actionTarget,omitempty"`
	VerificationMethod *string                             `json:"verificationMethod,omitempty"`
	TaskIcon           *string                             `json:"taskIcon,omitempty"`
	ButtonText         *string                             `json:"buttonText,omitempty"`
	StartDate          *int64                              `json:"startDate,omitempty"`
	EndDate            *int64                              `json:"endDate,omitempty"`
	SortOrder          *int                                `json:"sortOrder,omitempty"`
}

type CreateInfiniteAgentConfigInput struct {
	UserID          string     `json:"userID"`
	CommissionRateN float64    `json:"commissionRateN"`
	Status          StatusType `json:"status"`
}

type CreateInfiniteAgentConfigResponse struct {
	InfiniteAgentConfig *InfiniteAgentConfig `json:"infiniteAgentConfig,omitempty"`
	Success             bool                 `json:"success"`
	Message             string               `json:"message"`
}

type CreateInfiniteAgentInput struct {
	UserID          string  `json:"userId"`
	CommissionRateN float64 `json:"commissionRateN"`
	Status          string  `json:"status"`
}

type CreateTaskCategoryInput struct {
	Name        TaskCategoryName `json:"name"`
	DisplayName string           `json:"displayName"`
	Description *string          `json:"description,omitempty"`
	Icon        *string          `json:"icon,omitempty"`
	SortOrder   *int             `json:"sortOrder,omitempty"`
}

type CreateTaskInput struct {
	CategoryID         string                 `json:"categoryId"`
	Name               *MultilingualNameInput `json:"name"`
	Description        *string                `json:"description,omitempty"`
	Frequency          TaskFrequency          `json:"frequency"`
	TaskIdentifier     *TaskIdentifier        `json:"taskIdentifier,omitempty"`
	Points             int                    `json:"points"`
	MaxCompletions     *int                   `json:"maxCompletions,omitempty"`
	ResetPeriod        *string                `json:"resetPeriod,omitempty"`
	Conditions         *string                `json:"conditions,omitempty"`
	ActionTarget       *string                `json:"actionTarget,omitempty"`
	VerificationMethod *string                `json:"verificationMethod,omitempty"`
	ExternalLink       *string                `json:"externalLink,omitempty"`
	TaskIcon           *string                `json:"taskIcon,omitempty"`
	ButtonText         *string                `json:"buttonText,omitempty"`
	StartDate          *int64                 `json:"startDate,omitempty"`
	EndDate            *int64                 `json:"endDate,omitempty"`
	SortOrder          *int                   `json:"sortOrder,omitempty"`
}

type CreateTierBenefitInput struct {
	TierLevel                   int      `json:"tierLevel"`
	TierName                    string   `json:"tierName"`
	MinPoints                   int      `json:"minPoints"`
	CashbackPercentage          float64  `json:"cashbackPercentage"`
	ReferredIncentivePercentage *float64 `json:"referredIncentivePercentage,omitempty"`
	NetFee                      float64  `json:"netFee"`
	BenefitsDescription         *string  `json:"benefitsDescription,omitempty"`
	TierColor                   *string  `json:"tierColor,omitempty"`
	TierIcon                    *string  `json:"tierIcon,omitempty"`
}

type CreateTreeSnapshotInput struct {
	RootUserID string `json:"rootUserId"`
}

type DailyCompletionStat struct {
	Date            string `json:"date"`
	CompletionCount int    `json:"completionCount"`
}

type DeleteAgentLevelResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
}

type GetInfiniteAgentsInput struct {
	Page      *int    `json:"page,omitempty"`
	PageSize  *int    `json:"pageSize,omitempty"`
	Status    *string `json:"status,omitempty"`
	SortBy    *string `json:"sortBy,omitempty"`
	SortOrder *string `json:"sortOrder,omitempty"`
}

type GetReferralTreesInput struct {
	Page      *int    `json:"page,omitempty"`
	PageSize  *int    `json:"pageSize,omitempty"`
	SortBy    *string `json:"sortBy,omitempty"`
	SortOrder *string `json:"sortOrder,omitempty"`
}

type GetTopAgentsInput struct {
	Limit  *int   `json:"limit,omitempty"`
	SortBy string `json:"sortBy"`
}

type GetUsersByLevelInput struct {
	LevelID   int     `json:"levelId"`
	Page      *int    `json:"page,omitempty"`
	PageSize  *int    `json:"pageSize,omitempty"`
	SortBy    *string `json:"sortBy,omitempty"`
	SortOrder *string `json:"sortOrder,omitempty"`
}

type InfiniteAgentConfig struct {
	ID              string    `json:"id"`
	UserID          string    `json:"userID"`
	CommissionRateN float64   `json:"commissionRateN"`
	Status          string    `json:"Status"`
	CreatedAt       time.Time `json:"createdAt"`
	UpdatedAt       time.Time `json:"updatedAt"`
	User            *User     `json:"user,omitempty"`
}

type InfiniteAgentReferralTree struct {
	ID                    string    `json:"id"`
	CreatedAt             time.Time `json:"createdAt"`
	InfiniteAgentUserID   string    `json:"infiniteAgentUserId"`
	CommissionRateN       float64   `json:"commissionRateN"`
	RootUserID            string    `json:"rootUserId"`
	SnapshotDate          time.Time `json:"snapshotDate"`
	TotalNodes            int       `json:"totalNodes"`
	MaxDepth              int       `json:"maxDepth"`
	DirectCount           int       `json:"directCount"`
	ActiveUsers           int       `json:"activeUsers"`
	TradingUsers          int       `json:"tradingUsers"`
	TotalCommissionEarned float64   `json:"totalCommissionEarned"`
	TotalVolumeUsd        float64   `json:"totalVolumeUsd"`
	Status                string    `json:"status"`
	Description           *string   `json:"description,omitempty"`
}

type MultilingualName struct {
	En string  `json:"en"`
	Zh *string `json:"zh,omitempty"`
	Ja *string `json:"ja,omitempty"`
	Hi *string `json:"hi,omitempty"`
	Hk *string `json:"hk,omitempty"`
	Vi *string `json:"vi,omitempty"`
}

type MultilingualNameInput struct {
	En string  `json:"en"`
	Zh *string `json:"zh,omitempty"`
	Ja *string `json:"ja,omitempty"`
	Hi *string `json:"hi,omitempty"`
	Hk *string `json:"hk,omitempty"`
	Vi *string `json:"vi,omitempty"`
}

type Mutation struct {
}

type Query struct {
}

type ReferralSnapshotFull struct {
	UserID                   string  `json:"userId"`
	DirectCount              int     `json:"directCount"`
	TotalDownlineCount       int     `json:"totalDownlineCount"`
	TotalVolumeUsd           float64 `json:"totalVolumeUsd"`
	TotalRewardsDistributed  float64 `json:"totalRewardsDistributed"`
	TradingUserCount         int     `json:"tradingUserCount"`
	TotalPerpsVolumeUsd      float64 `json:"totalPerpsVolumeUsd"`
	TotalPerpsFees           float64 `json:"totalPerpsFees"`
	TotalPerpsFeesPaid       float64 `json:"totalPerpsFeesPaid"`
	TotalPerpsFeesUnpaid     float64 `json:"totalPerpsFeesUnpaid"`
	TotalPerpsTradesCount    int     `json:"totalPerpsTradesCount"`
	TotalMemeVolumeUsd       float64 `json:"totalMemeVolumeUsd"`
	TotalMemeFees            float64 `json:"totalMemeFees"`
	TotalMemeFeesPaid        float64 `json:"totalMemeFeesPaid"`
	TotalMemeFeesUnpaid      float64 `json:"totalMemeFeesUnpaid"`
	TotalMemeTradesCount     int     `json:"totalMemeTradesCount"`
	TotalCommissionEarnedUsd float64 `json:"totalCommissionEarnedUsd"`
	ClaimedCommissionUsd     float64 `json:"claimedCommissionUsd"`
	UnclaimedCommissionUsd   float64 `json:"unclaimedCommissionUsd"`
	TotalCashbackEarnedUsd   float64 `json:"totalCashbackEarnedUsd"`
	ClaimedCashbackUsd       float64 `json:"claimedCashbackUsd"`
	UnclaimedCashbackUsd     float64 `json:"unclaimedCashbackUsd"`
	L1UplineID               *string `json:"l1UplineId,omitempty"`
	L2UplineID               *string `json:"l2UplineId,omitempty"`
	L3UplineID               *string `json:"l3UplineId,omitempty"`
}

type ReferralTreesResponse struct {
	Trees      []*InfiniteAgentReferralTree `json:"trees"`
	Total      int                          `json:"total"`
	Page       int                          `json:"page"`
	PageSize   int                          `json:"pageSize"`
	TotalPages int                          `json:"totalPages"`
}

type SystemOperationResult struct {
	Success        bool   `json:"success"`
	ProcessedCount int    `json:"processedCount"`
	ErrorCount     int    `json:"errorCount"`
	Message        string `json:"message"`
}

type TaskCategory struct {
	ID          string           `json:"id"`
	Name        TaskCategoryName `json:"name"`
	DisplayName string           `json:"displayName"`
	Description *string          `json:"description,omitempty"`
	Icon        *string          `json:"icon,omitempty"`
	IsActive    bool             `json:"isActive"`
	SortOrder   int              `json:"sortOrder"`
	CreatedAt   time.Time        `json:"createdAt"`
	UpdatedAt   time.Time        `json:"updatedAt"`
}

type TaskCompletionStat struct {
	TaskName        string `json:"taskName"`
	CompletionCount int    `json:"completionCount"`
}

type TierBenefit struct {
	ID                          string    `json:"id"`
	TierLevel                   int       `json:"tierLevel"`
	TierName                    string    `json:"tierName"`
	MinPoints                   int       `json:"minPoints"`
	CashbackPercentage          float64   `json:"cashbackPercentage"`
	ReferredIncentivePercentage float64   `json:"referredIncentivePercentage"`
	NetFee                      float64   `json:"netFee"`
	BenefitsDescription         *string   `json:"benefitsDescription,omitempty"`
	TierColor                   *string   `json:"tierColor,omitempty"`
	TierIcon                    *string   `json:"tierIcon,omitempty"`
	IsActive                    bool      `json:"isActive"`
	CreatedAt                   time.Time `json:"createdAt"`
	UpdatedAt                   time.Time `json:"updatedAt"`
}

type TierBenefitResponse struct {
	Success bool         `json:"success"`
	Message string       `json:"message"`
	Data    *TierBenefit `json:"data,omitempty"`
}

type TierDistributionStat struct {
	TierLevel int `json:"tierLevel"`
	UserCount int `json:"userCount"`
}

type TopAgent struct {
	UserID           string  `json:"userId"`
	InvitationCode   string  `json:"invitationCode"`
	Email            *string `json:"email,omitempty"`
	AgentLevel       string  `json:"agentLevel"`
	CommissionEarned float64 `json:"commissionEarned"`
	VolumeGenerated  float64 `json:"volumeGenerated"`
	ReferralCount    int     `json:"referralCount"`
	IsInfiniteAgent  bool    `json:"isInfiniteAgent"`
}

type UpdateAgentLevelInput struct {
	ID                      int     `json:"id"`
	Name                    string  `json:"name"`
	MemeVolumeThreshold     float64 `json:"memeVolumeThreshold"`
	ContractVolumeThreshold float64 `json:"contractVolumeThreshold"`
	MemeFeeRate             float64 `json:"memeFeeRate"`
	TakerFeeRate            float64 `json:"takerFeeRate"`
	MakerFeeRate            float64 `json:"makerFeeRate"`
	DirectCommissionRate    float64 `json:"directCommissionRate"`
	IndirectCommissionRate  float64 `json:"indirectCommissionRate"`
	ExtendedCommissionRate  float64 `json:"extendedCommissionRate"`
	MemeFeeRebate           float64 `json:"memeFeeRebate"`
}

type UpdateAgentLevelResponse struct {
	Level   *AgentLevel `json:"level,omitempty"`
	Success bool        `json:"success"`
	Message string      `json:"message"`
}

type UpdateCommissionRatesInput struct {
	LevelID                int     `json:"levelId"`
	DirectCommissionRate   float64 `json:"directCommissionRate"`
	IndirectCommissionRate float64 `json:"indirectCommissionRate"`
	ExtendedCommissionRate float64 `json:"extendedCommissionRate"`
}

type UpdateInfiniteAgentInput struct {
	ID              string   `json:"id"`
	CommissionRateN *float64 `json:"commissionRateN,omitempty"`
	Status          *string  `json:"status,omitempty"`
}

type UpdateTaskCategoryInput struct {
	ID          string            `json:"id"`
	Name        *TaskCategoryName `json:"name,omitempty"`
	DisplayName *string           `json:"displayName,omitempty"`
	Description *string           `json:"description,omitempty"`
	Icon        *string           `json:"icon,omitempty"`
	IsActive    *bool             `json:"isActive,omitempty"`
	SortOrder   *int              `json:"sortOrder,omitempty"`
}

type UpdateTaskInput struct {
	ID                 string                 `json:"id"`
	CategoryID         *string                `json:"categoryId,omitempty"`
	Name               *MultilingualNameInput `json:"name,omitempty"`
	Description        *string                `json:"description,omitempty"`
	Frequency          *TaskFrequency         `json:"frequency,omitempty"`
	TaskIdentifier     *TaskIdentifier        `json:"taskIdentifier,omitempty"`
	Points             *int                   `json:"points,omitempty"`
	MaxCompletions     *int                   `json:"maxCompletions,omitempty"`
	ResetPeriod        *string                `json:"resetPeriod,omitempty"`
	Conditions         *string                `json:"conditions,omitempty"`
	ActionTarget       *string                `json:"actionTarget,omitempty"`
	VerificationMethod *string                `json:"verificationMethod,omitempty"`
	ExternalLink       *string                `json:"externalLink,omitempty"`
	TaskIcon           *string                `json:"taskIcon,omitempty"`
	ButtonText         *string                `json:"buttonText,omitempty"`
	StartDate          *int64                 `json:"startDate,omitempty"`
	EndDate            *int64                 `json:"endDate,omitempty"`
	SortOrder          *int                   `json:"sortOrder,omitempty"`
	IsActive           *bool                  `json:"isActive,omitempty"`
}

type UpdateTierBenefitInput struct {
	ID                          string   `json:"id"`
	TierLevel                   *int     `json:"tierLevel,omitempty"`
	TierName                    *string  `json:"tierName,omitempty"`
	MinPoints                   *int     `json:"minPoints,omitempty"`
	CashbackPercentage          *float64 `json:"cashbackPercentage,omitempty"`
	ReferredIncentivePercentage *float64 `json:"referredIncentivePercentage,omitempty"`
	NetFee                      *float64 `json:"netFee,omitempty"`
	BenefitsDescription         *string  `json:"benefitsDescription,omitempty"`
	TierColor                   *string  `json:"tierColor,omitempty"`
	TierIcon                    *string  `json:"tierIcon,omitempty"`
	IsActive                    *bool    `json:"isActive,omitempty"`
}

type User struct {
	ID                        string      `json:"id"`
	Email                     *string     `json:"email,omitempty"`
	InvitationCode            *string     `json:"invitationCode,omitempty"`
	CreatedAt                 string      `json:"createdAt"`
	UpdatedAt                 string      `json:"updatedAt"`
	DeletedAt                 *string     `json:"deletedAt,omitempty"`
	AgentLevelID              int         `json:"agentLevelId"`
	AgentLevel                *AgentLevel `json:"agentLevel"`
	LevelGracePeriodStartedAt *string     `json:"levelGracePeriodStartedAt,omitempty"`
	LevelUpgradedAt           *string     `json:"levelUpgradedAt,omitempty"`
	FirstTransactionAt        *string     `json:"firstTransactionAt,omitempty"`
}

type UserTierInfo struct {
	UserID               string       `json:"userId"`
	Email                *string      `json:"email,omitempty"`
	CurrentTier          *TierBenefit `json:"currentTier,omitempty"`
	TotalPoints          int          `json:"totalPoints"`
	AvailableCashback    float64      `json:"availableCashback"`
	TotalCashbackClaimed float64      `json:"totalCashbackClaimed"`
	NextTier             *TierBenefit `json:"nextTier,omitempty"`
	PointsToNextTier     *int         `json:"pointsToNextTier,omitempty"`
	CreatedAt            time.Time    `json:"createdAt"`
	LastActivityAt       *time.Time   `json:"lastActivityAt,omitempty"`
}

type UserTierInfoInput struct {
	UserID string `json:"userId"`
}

type UsersByLevelResponse struct {
	Users      []*User `json:"users"`
	Total      int     `json:"total"`
	Page       int     `json:"page"`
	PageSize   int     `json:"pageSize"`
	TotalPages int     `json:"totalPages"`
}

type StatusType string

const (
	StatusTypeActive   StatusType = "ACTIVE"
	StatusTypeInactive StatusType = "INACTIVE"
)

var AllStatusType = []StatusType{
	StatusTypeActive,
	StatusTypeInactive,
}

func (e StatusType) IsValid() bool {
	switch e {
	case StatusTypeActive, StatusTypeInactive:
		return true
	}
	return false
}

func (e StatusType) String() string {
	return string(e)
}

func (e *StatusType) UnmarshalGQL(v any) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = StatusType(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid StatusType", str)
	}
	return nil
}

func (e StatusType) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

func (e *StatusType) UnmarshalJSON(b []byte) error {
	s, err := strconv.Unquote(string(b))
	if err != nil {
		return err
	}
	return e.UnmarshalGQL(s)
}

func (e StatusType) MarshalJSON() ([]byte, error) {
	var buf bytes.Buffer
	e.MarshalGQL(&buf)
	return buf.Bytes(), nil
}

type TaskCategoryName string

const (
	TaskCategoryNameDaily     TaskCategoryName = "DAILY"
	TaskCategoryNameCommunity TaskCategoryName = "COMMUNITY"
	TaskCategoryNameTrading   TaskCategoryName = "TRADING"
)

var AllTaskCategoryName = []TaskCategoryName{
	TaskCategoryNameDaily,
	TaskCategoryNameCommunity,
	TaskCategoryNameTrading,
}

func (e TaskCategoryName) IsValid() bool {
	switch e {
	case TaskCategoryNameDaily, TaskCategoryNameCommunity, TaskCategoryNameTrading:
		return true
	}
	return false
}

func (e TaskCategoryName) String() string {
	return string(e)
}

func (e *TaskCategoryName) UnmarshalGQL(v any) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = TaskCategoryName(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid TaskCategoryName", str)
	}
	return nil
}

func (e TaskCategoryName) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

func (e *TaskCategoryName) UnmarshalJSON(b []byte) error {
	s, err := strconv.Unquote(string(b))
	if err != nil {
		return err
	}
	return e.UnmarshalGQL(s)
}

func (e TaskCategoryName) MarshalJSON() ([]byte, error) {
	var buf bytes.Buffer
	e.MarshalGQL(&buf)
	return buf.Bytes(), nil
}

type TaskFrequency string

const (
	TaskFrequencyDaily       TaskFrequency = "DAILY"
	TaskFrequencyProgressive TaskFrequency = "PROGRESSIVE"
	TaskFrequencyOneTime     TaskFrequency = "ONE_TIME"
	TaskFrequencyManual      TaskFrequency = "MANUAL"
	TaskFrequencyUnlimited   TaskFrequency = "UNLIMITED"
)

var AllTaskFrequency = []TaskFrequency{
	TaskFrequencyDaily,
	TaskFrequencyProgressive,
	TaskFrequencyOneTime,
	TaskFrequencyManual,
	TaskFrequencyUnlimited,
}

func (e TaskFrequency) IsValid() bool {
	switch e {
	case TaskFrequencyDaily, TaskFrequencyProgressive, TaskFrequencyOneTime, TaskFrequencyManual, TaskFrequencyUnlimited:
		return true
	}
	return false
}

func (e TaskFrequency) String() string {
	return string(e)
}

func (e *TaskFrequency) UnmarshalGQL(v any) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = TaskFrequency(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid TaskFrequency", str)
	}
	return nil
}

func (e TaskFrequency) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

func (e *TaskFrequency) UnmarshalJSON(b []byte) error {
	s, err := strconv.Unquote(string(b))
	if err != nil {
		return err
	}
	return e.UnmarshalGQL(s)
}

func (e TaskFrequency) MarshalJSON() ([]byte, error) {
	var buf bytes.Buffer
	e.MarshalGQL(&buf)
	return buf.Bytes(), nil
}

type TaskIdentifier string

const (
	TaskIdentifierDailyCheckin        TaskIdentifier = "DAILY_CHECKIN"
	TaskIdentifierMemeTradeDaily      TaskIdentifier = "MEME_TRADE_DAILY"
	TaskIdentifierPerpetualTradeDaily TaskIdentifier = "PERPETUAL_TRADE_DAILY"
	TaskIdentifierMarketPageView      TaskIdentifier = "MARKET_PAGE_VIEW"
	TaskIdentifierCheckMarketTrends   TaskIdentifier = "CHECK_MARKET_TRENDS"
	TaskIdentifierTwitterFollow       TaskIdentifier = "TWITTER_FOLLOW"
	TaskIdentifierTwitterRetweet      TaskIdentifier = "TWITTER_RETWEET"
	TaskIdentifierTwitterLike         TaskIdentifier = "TWITTER_LIKE"
	TaskIdentifierTelegramJoin        TaskIdentifier = "TELEGRAM_JOIN"
	TaskIdentifierInviteFriends       TaskIdentifier = "INVITE_FRIENDS"
	TaskIdentifierShareReferral       TaskIdentifier = "SHARE_REFERRAL"
	TaskIdentifierShareEarningsChart  TaskIdentifier = "SHARE_EARNINGS_CHART"
	TaskIdentifierTradingPoints       TaskIdentifier = "TRADING_POINTS"
)

var AllTaskIdentifier = []TaskIdentifier{
	TaskIdentifierDailyCheckin,
	TaskIdentifierMemeTradeDaily,
	TaskIdentifierPerpetualTradeDaily,
	TaskIdentifierMarketPageView,
	TaskIdentifierCheckMarketTrends,
	TaskIdentifierTwitterFollow,
	TaskIdentifierTwitterRetweet,
	TaskIdentifierTwitterLike,
	TaskIdentifierTelegramJoin,
	TaskIdentifierInviteFriends,
	TaskIdentifierShareReferral,
	TaskIdentifierShareEarningsChart,
	TaskIdentifierTradingPoints,
}

func (e TaskIdentifier) IsValid() bool {
	switch e {
	case TaskIdentifierDailyCheckin, TaskIdentifierMemeTradeDaily, TaskIdentifierPerpetualTradeDaily, TaskIdentifierMarketPageView, TaskIdentifierCheckMarketTrends, TaskIdentifierTwitterFollow, TaskIdentifierTwitterRetweet, TaskIdentifierTwitterLike, TaskIdentifierTelegramJoin, TaskIdentifierInviteFriends, TaskIdentifierShareReferral, TaskIdentifierShareEarningsChart, TaskIdentifierTradingPoints:
		return true
	}
	return false
}

func (e TaskIdentifier) String() string {
	return string(e)
}

func (e *TaskIdentifier) UnmarshalGQL(v any) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = TaskIdentifier(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid TaskIdentifier", str)
	}
	return nil
}

func (e TaskIdentifier) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

func (e *TaskIdentifier) UnmarshalJSON(b []byte) error {
	s, err := strconv.Unquote(string(b))
	if err != nil {
		return err
	}
	return e.UnmarshalGQL(s)
}

func (e TaskIdentifier) MarshalJSON() ([]byte, error) {
	var buf bytes.Buffer
	e.MarshalGQL(&buf)
	return buf.Bytes(), nil
}
