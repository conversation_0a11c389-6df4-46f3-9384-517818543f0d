package activity_cashback

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"go.uber.org/zap"
)

// SystemInitializer initializes the entire Activity Cashback system
type SystemInitializer struct {
	adminService  AdminServiceInterface
	taskSeeder    *TaskSeeder
	isInitialized bool
}

// NewSystemInitializer creates a new SystemInitializer
func NewSystemInitializer() *SystemInitializer {
	return &SystemInitializer{
		adminService:  NewAdminService(),
		taskSeeder:    NewTaskSeeder(),
		isInitialized: false,
	}
}

// Initialize initializes the entire Activity Cashback system
func (si *SystemInitializer) Initialize(ctx context.Context) error {
	if si.isInitialized {
		global.GVA_LOG.Warn("Activity Cashback system is already initialized")
		return nil
	}

	global.GVA_LOG.Info("Initializing Activity Cashback System")

	// Step 1: Seed initial tasks and categories
	if err := si.seedInitialData(ctx); err != nil {
		global.GVA_LOG.Error("Failed to seed initial data", zap.Error(err))
		return err
	}

	// Step 2: Background jobs are now handled by the scheduled task system
	global.GVA_LOG.Info("Background jobs will be handled by the scheduled task system")

	// Step 3: Perform initial system checks
	if err := si.performSystemChecks(ctx); err != nil {
		global.GVA_LOG.Error("System checks failed", zap.Error(err))
		return err
	}

	si.isInitialized = true
	global.GVA_LOG.Info("Activity Cashback System initialized successfully")

	return nil
}

// Shutdown gracefully shuts down the Activity Cashback system
func (si *SystemInitializer) Shutdown() {
	if !si.isInitialized {
		return
	}

	global.GVA_LOG.Info("Shutting down Activity Cashback System")

	// Background jobs are now handled by the scheduled task system
	global.GVA_LOG.Info("Background jobs are handled by the scheduled task system")

	si.isInitialized = false
	global.GVA_LOG.Info("Activity Cashback System shutdown completed")
}

// IsInitialized returns whether the system is initialized
func (si *SystemInitializer) IsInitialized() bool {
	return si.isInitialized
}

// seedInitialData seeds initial data into the database
func (si *SystemInitializer) seedInitialData(ctx context.Context) error {
	global.GVA_LOG.Info("Seeding initial Activity Cashback data")

	// Step 1: Seed tier benefits first (required for user tier calculations)
	if err := si.seedTierBenefits(ctx); err != nil {
		return err
	}

	// Step 2: Seed task categories
	if err := si.seedTaskCategories(ctx); err != nil {
		return err
	}

	// Step 3: Seed initial tasks only if enabled in configuration
	if global.GVA_CONFIG.System.EnableTaskSeeder {
		global.GVA_LOG.Info("Task seeder is enabled, proceeding with task seeding")
		if err := si.taskSeeder.SeedTasks(ctx); err != nil {
			return err
		}
	} else {
		global.GVA_LOG.Info("Task seeder is disabled, skipping task seeding")
	}

	global.GVA_LOG.Info("Initial data seeding completed")
	return nil
}

// seedTierBenefits seeds initial tier benefits
func (si *SystemInitializer) seedTierBenefits(ctx context.Context) error {
	global.GVA_LOG.Info("Seeding tier benefits")

	benefits := []struct {
		tierLevel                   int
		tierName                    string
		minPoints                   int
		cashbackPercentage          float64
		benefitsDescription         string
		tierColor                   string
		tierIcon                    string
		netFee                      float64
		referredIncentivePercentage float64
	}{
		{
			tierLevel:                   1,
			tierName:                    "Bronze",
			minPoints:                   0,
			cashbackPercentage:          0.0000, // 0% cashback
			benefitsDescription:         "Basic tier with 0% cashback on all trades",
			tierColor:                   "#CD7F32",
			tierIcon:                    "bronze-medal",
			netFee:                      0.0095, // 0.95% net fee
			referredIncentivePercentage: 0.0500, // 5% referred incentive
		},
		{
			tierLevel:                   2,
			tierName:                    "Silver",
			minPoints:                   1000,
			cashbackPercentage:          0.0500, // 5% cashback
			benefitsDescription:         "Silver tier with 5% cashback on all trades",
			tierColor:                   "#C0C0C0",
			tierIcon:                    "silver-medal",
			netFee:                      0.0090, // 0.90% net fee
			referredIncentivePercentage: 0.0500, // 5% referred incentive
		},
		{
			tierLevel:                   3,
			tierName:                    "Gold",
			minPoints:                   5000,
			cashbackPercentage:          0.1000, // 10% cashback
			benefitsDescription:         "Gold tier with 10% cashback on all trades",
			tierColor:                   "#FFD700",
			tierIcon:                    "gold-medal",
			netFee:                      0.0085, // 0.85% net fee
			referredIncentivePercentage: 0.0500, // 5% referred incentive
		},
		{
			tierLevel:                   4,
			tierName:                    "Platinum",
			minPoints:                   15000,
			cashbackPercentage:          0.1500, // 15% cashback
			benefitsDescription:         "Platinum tier with 15% cashback on all trades",
			tierColor:                   "#E5E4E2",
			tierIcon:                    "platinum-medal",
			netFee:                      0.0080, // 0.80% net fee
			referredIncentivePercentage: 0.0500, // 5% referred incentive
		},
		{
			tierLevel:                   5,
			tierName:                    "Diamond",
			minPoints:                   50000,
			cashbackPercentage:          0.2000, // 20% cashback
			benefitsDescription:         "Diamond tier with 20% cashback on all trades",
			tierColor:                   "#B9F2FF",
			tierIcon:                    "diamond-medal",
			netFee:                      0.0075, // 0.75% net fee
			referredIncentivePercentage: 0.0500, // 5% referred incentive
		},
	}

	for _, benefit := range benefits {
		// Check if tier benefit already exists
		existingBenefits, err := si.adminService.GetTierBenefits(ctx)
		if err != nil {
			return err
		}

		exists := false
		for _, existing := range existingBenefits {
			if existing.TierLevel == benefit.tierLevel {
				exists = true
				break
			}
		}

		if !exists {
			tierBenefit := &model.TierBenefit{
				TierLevel:                   benefit.tierLevel,
				TierName:                    benefit.tierName,
				MinPoints:                   benefit.minPoints,
				CashbackPercentage:          decimal.NewFromFloat(benefit.cashbackPercentage),
				ReferredIncentivePercentage: decimal.NewFromFloat(benefit.referredIncentivePercentage),
				NetFee:                      decimal.NewFromFloat(benefit.netFee),
				BenefitsDescription:         &benefit.benefitsDescription,
				TierColor:                   &benefit.tierColor,
				TierIcon:                    &benefit.tierIcon,
				IsActive:                    true,
			}

			if err := si.adminService.CreateTierBenefit(ctx, tierBenefit); err != nil {
				return err
			}

			global.GVA_LOG.Info("Tier benefit created",
				zap.Int("tier_level", benefit.tierLevel),
				zap.String("tier_name", benefit.tierName),
				zap.Float64("cashback_percentage", benefit.cashbackPercentage),
				zap.Float64("net_fee", benefit.netFee))
		}
	}

	return nil
}

// seedTaskCategories seeds initial task categories
func (si *SystemInitializer) seedTaskCategories(ctx context.Context) error {
	categories := []struct {
		name        model.TaskCategoryName
		displayName string
		description string
		icon        string
		sortOrder   int
	}{
		{
			name:        model.CategoryDaily,
			displayName: model.CategoryDaily.GetDisplayName(),
			description: model.CategoryDaily.GetDescription(),
			icon:        model.CategoryDaily.GetIcon(),
			sortOrder:   model.CategoryDaily.GetSortOrder(),
		},
		{
			name:        model.CategoryCommunity,
			displayName: model.CategoryCommunity.GetDisplayName(),
			description: model.CategoryCommunity.GetDescription(),
			icon:        model.CategoryCommunity.GetIcon(),
			sortOrder:   model.CategoryCommunity.GetSortOrder(),
		},
		{
			name:        model.CategoryTrading,
			displayName: model.CategoryTrading.GetDisplayName(),
			description: model.CategoryTrading.GetDescription(),
			icon:        model.CategoryTrading.GetIcon(),
			sortOrder:   model.CategoryTrading.GetSortOrder(),
		},
	}

	for _, cat := range categories {
		// Check if category already exists
		existingCategories, err := si.adminService.GetTaskCategories(ctx)
		if err != nil {
			return err
		}

		exists := false
		for _, existing := range existingCategories {
			if existing.Name == cat.name {
				exists = true
				break
			}
		}

		if !exists {
			category := &model.TaskCategory{
				Name:        cat.name,
				DisplayName: cat.displayName,
				Description: &cat.description,
				Icon:        &cat.icon,
				IsActive:    true,
				SortOrder:   cat.sortOrder,
			}

			if err := si.adminService.CreateTaskCategory(ctx, category); err != nil {
				return err
			}

			global.GVA_LOG.Info("Task category created", zap.String("name", string(cat.name)))
		}
	}

	return nil
}

// performSystemChecks performs initial system health checks
func (si *SystemInitializer) performSystemChecks(ctx context.Context) error {
	global.GVA_LOG.Info("Performing Activity Cashback system checks")

	// Check 1: Verify task categories exist
	categories, err := si.adminService.GetTaskCategories(ctx)
	if err != nil {
		return err
	}
	if len(categories) == 0 {
		global.GVA_LOG.Warn("No task categories found")
	} else {
		global.GVA_LOG.Info("Task categories verified", zap.Int("count", len(categories)))
	}

	// Check 2: Verify tier benefits exist
	benefits, err := si.adminService.GetTierBenefits(ctx)
	if err != nil {
		return err
	}
	if len(benefits) == 0 {
		global.GVA_LOG.Warn("No tier benefits found")
	} else {
		global.GVA_LOG.Info("Tier benefits verified", zap.Int("count", len(benefits)))
	}

	// Check 3: Verify tasks exist
	tasks, err := si.adminService.GetAllTasks(ctx)
	if err != nil {
		return err
	}
	if len(tasks) == 0 {
		global.GVA_LOG.Warn("No tasks found")
	} else {
		global.GVA_LOG.Info("Tasks verified", zap.Int("count", len(tasks)))
	}

	// Check 4: Background jobs are now handled by scheduled tasks
	global.GVA_LOG.Info("Background jobs are handled by the scheduled task system")

	global.GVA_LOG.Info("All system checks passed")
	return nil
}

// GetSystemStatus returns the current system status
func (si *SystemInitializer) GetSystemStatus(ctx context.Context) map[string]interface{} {
	status := map[string]interface{}{
		"initialized":  si.isInitialized,
		"startup_time": time.Now().UTC(),
	}

	if si.isInitialized {
		// Get task statistics
		tasks, err := si.adminService.GetAllTasks(ctx)
		if err == nil {
			status["total_tasks"] = len(tasks)

			// Count tasks by type
			tasksByCategory := make(map[string]int)
			for _, task := range tasks {
				tasksByCategory[string(task.Category.Name)]++
			}
			status["tasks_by_category"] = tasksByCategory
		}

		// Get tier statistics
		benefits, err := si.adminService.GetTierBenefits(ctx)
		if err == nil {
			status["total_tiers"] = len(benefits)
		}

		// Get tier distribution
		distribution, err := si.adminService.GetTierDistribution(ctx)
		if err == nil {
			status["tier_distribution"] = distribution
		}

		// Background jobs are now handled by scheduled tasks
		status["background_jobs"] = map[string]interface{}{
			"status": "handled_by_scheduled_tasks",
			"note":   "Background jobs have been migrated to the scheduled task system",
		}
	}

	return status
}

// ProcessExternalEvent processes events from external systems
func (si *SystemInitializer) ProcessExternalEvent(ctx context.Context, eventType string, userID string, data map[string]interface{}) error {
	if !si.isInitialized {
		global.GVA_LOG.Warn("Activity Cashback system not initialized, ignoring event",
			zap.String("event_type", eventType),
			zap.String("user_id", userID))
		return nil
	}

	// Create service and processor manager for event processing
	service := NewActivityCashbackService()
	processorManager := NewTaskManager(service)

	// Parse user ID to UUID
	userUUID, err := uuid.Parse(userID)
	if err != nil {
		return fmt.Errorf("invalid user ID: %w", err)
	}

	switch eventType {
	case "trade_completed":
		return processorManager.ProcessTradingEvent(ctx, userUUID, data)
	case "user_login":
		// Process daily check-in
		loginData := map[string]interface{}{
			"login_time": time.Now(),
		}
		return processorManager.ProcessTaskByIdentifier(ctx, userUUID, model.TaskIDDailyCheckin, "daily", loginData)
	case "market_check":
		// Process market check
		marketData := map[string]interface{}{
			"check_time": time.Now(),
		}
		return processorManager.ProcessTaskByIdentifier(ctx, userUUID, model.TaskIDMarketPageView, "daily", marketData)
	default:
		global.GVA_LOG.Warn("Unknown event type", zap.String("event_type", eventType))
		return nil
	}
}

// ForceTaskReset forces a reset of all tasks (admin function)
func (si *SystemInitializer) ForceTaskReset(ctx context.Context, resetType string) error {
	if !si.isInitialized {
		return nil
	}

	global.GVA_LOG.Info("Forcing task reset", zap.String("reset_type", resetType))

	switch resetType {
	case "daily":
		return si.adminService.ResetAllDailyTasks(ctx)
	case "weekly":
		return si.adminService.ResetAllWeeklyTasks(ctx)
	case "monthly":
		return si.adminService.ResetAllMonthlyTasks(ctx)
	default:
		return nil
	}
}

// RecalculateAllTiers recalculates tiers for all users (admin function)
func (si *SystemInitializer) RecalculateAllTiers(ctx context.Context) error {
	if !si.isInitialized {
		return nil
	}

	global.GVA_LOG.Info("Recalculating all user tiers")
	return si.adminService.RecalculateAllUserTiers(ctx)
}

// GetHealthCheck returns a health check for the Activity Cashback system
func (si *SystemInitializer) GetHealthCheck(ctx context.Context) map[string]interface{} {
	health := map[string]interface{}{
		"status":      "unknown",
		"initialized": si.isInitialized,
		"timestamp":   time.Now().UTC(),
	}

	if !si.isInitialized {
		health["status"] = "not_initialized"
		return health
	}

	// Background jobs are now handled by scheduled tasks
	global.GVA_LOG.Info("Background jobs are handled by the scheduled task system")

	// Try to get basic data to verify database connectivity
	_, err := si.adminService.GetTaskCategories(ctx)
	if err != nil {
		health["status"] = "unhealthy"
		health["reason"] = "database_connectivity_issue"
		health["error"] = err.Error()
		return health
	}

	health["status"] = "healthy"
	return health
}

// Global instance for easy access
var globalSystemInitializer *SystemInitializer

// InitializeGlobalSystem initializes the global Activity Cashback system
func InitializeGlobalSystem(ctx context.Context) error {
	if globalSystemInitializer == nil {
		globalSystemInitializer = NewSystemInitializer()
	}
	return globalSystemInitializer.Initialize(ctx)
}

// ShutdownGlobalSystem shuts down the global Activity Cashback system
func ShutdownGlobalSystem() {
	if globalSystemInitializer != nil {
		globalSystemInitializer.Shutdown()
	}
}

// GetGlobalSystemInitializer returns the global system initializer
func GetGlobalSystemInitializer() *SystemInitializer {
	return globalSystemInitializer
}

// ProcessGlobalEvent processes events through the global system
func ProcessGlobalEvent(ctx context.Context, eventType string, userID string, data map[string]interface{}) error {
	if globalSystemInitializer == nil {
		return nil
	}
	return globalSystemInitializer.ProcessExternalEvent(ctx, eventType, userID, data)
}
