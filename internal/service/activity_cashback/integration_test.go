package activity_cashback

import (
	"context"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/suite"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/test"
)

// ActivityCashbackIntegrationTestSuite is the integration test suite
type ActivityCashbackIntegrationTestSuite struct {
	suite.Suite
	service          ActivityCashbackServiceInterface
	adminService     AdminServiceInterface
	processorManager *TaskManager
	ctx              context.Context
	testUserID       uuid.UUID
	testTaskID       uuid.UUID
}

// SetupSuite sets up the test suite
func (suite *ActivityCashbackIntegrationTestSuite) SetupSuite() {
	suite.ctx = context.Background()
	suite.testUserID = uuid.New()
	suite.testTaskID = uuid.New()

	// Setup test configuration and database
	test.SetupTestConfig()
	test.SetupTestDB()

	// Initialize services (in real integration tests, you'd use test database)
	suite.service = NewActivityCashbackService()
	suite.adminService = NewAdminService()
	suite.processorManager = NewTaskManager(suite.service)
}

// TearDownSuite cleans up the test suite
func (suite *ActivityCashbackIntegrationTestSuite) TearDownSuite() {
	test.CleanupTestConfig()
}

// TestUserInitialization tests user initialization flow
func (suite *ActivityCashbackIntegrationTestSuite) TestUserInitialization() {
	// Use a unique user ID for this test
	userID := uuid.New()

	// Test user initialization
	err := suite.service.InitializeUserForActivityCashback(suite.ctx, userID)
	suite.NoError(err)

	// Verify user tier info was created
	tierInfo, err := suite.service.GetUserTierInfo(suite.ctx, userID)
	suite.NoError(err)
	suite.NotNil(tierInfo)
	suite.Equal(1, tierInfo.CurrentTier) // Should start at tier 1
	suite.Equal(0, tierInfo.TotalPoints) // Should start with 0 points
}

// TestTaskCreationAndCompletion tests the full task lifecycle
func (suite *ActivityCashbackIntegrationTestSuite) TestTaskCreationAndCompletion() {
	// Create a test task category first (this would normally be seeded)
	category := &model.TaskCategory{
		Name:        model.TaskCategoryName("test_category"),
		DisplayName: "Test Category",
		IsActive:    true,
		SortOrder:   1,
	}
	err := suite.adminService.CreateTaskCategory(suite.ctx, category)
	suite.NoError(err)

	// Create a test task
	task := &model.ActivityTask{
		CategoryID:  category.ID,
		Name:        "Test Daily Task",
		Description: &[]string{"Complete this test task"}[0],
		Frequency:   model.FrequencyDaily,
		Points:      10,
		IsActive:    true,
		SortOrder:   1,
	}
	err = suite.adminService.CreateTask(suite.ctx, task, uuid.New())
	suite.NoError(err)
	suite.testTaskID = task.ID

	// Use a unique user ID for this test
	userID := uuid.New()

	// Initialize user for the task
	err = suite.service.InitializeUserForActivityCashback(suite.ctx, userID)
	suite.NoError(err)

	// Complete the task
	verificationData := map[string]interface{}{
		"completed_at": time.Now(),
	}
	err = suite.service.CompleteTask(suite.ctx, userID, suite.testTaskID, verificationData)
	suite.NoError(err)

	// Verify points were awarded
	tierInfo, err := suite.service.GetUserTierInfo(suite.ctx, userID)
	suite.NoError(err)
	suite.Equal(10, tierInfo.TotalPoints)

	// Verify task progress
	progress, err := suite.service.GetTaskProgress(suite.ctx, userID, suite.testTaskID)
	suite.NoError(err)
	suite.Equal(model.TaskStatusCompleted, progress.Status)
}

// TestTradingTaskFlow tests trading task processing
func (suite *ActivityCashbackIntegrationTestSuite) TestTradingTaskFlow() {
	// Simulate a trading event
	tradeData := map[string]interface{}{
		"volume":     1000.0,
		"trade_type": "MEME",
		"symbol":     "TEST/USDT",
	}

	// Process the trading event
	err := suite.processorManager.ProcessTradingEvent(suite.ctx, suite.testUserID, tradeData)
	suite.NoError(err)

	// Verify that trading points were awarded
	tierInfo, err := suite.service.GetUserTierInfo(suite.ctx, suite.testUserID)
	suite.NoError(err)
	suite.Greater(tierInfo.TotalPoints, 0) // Should have earned some points
}

// TestTierProgression tests tier upgrade functionality
func (suite *ActivityCashbackIntegrationTestSuite) TestTierProgression() {
	// Use a unique user ID for this test
	userID := uuid.New()

	// Initialize user first
	err := suite.service.InitializeUserForActivityCashback(suite.ctx, userID)
	suite.NoError(err)

	// Add enough points to trigger tier upgrade
	err = suite.service.AddPoints(suite.ctx, userID, 500, "test_points")
	suite.NoError(err)

	// Check for tier upgrade
	newTier, err := suite.service.CheckTierUpgrade(suite.ctx, userID)
	suite.NoError(err)

	if newTier != nil {
		// Verify tier was upgraded
		tierInfo, err := suite.service.GetUserTierInfo(suite.ctx, userID)
		suite.NoError(err)
		suite.Greater(tierInfo.CurrentTier, 1)
	}
}

// TestTaskReset tests task reset functionality
func (suite *ActivityCashbackIntegrationTestSuite) TestTaskReset() {
	// Use a unique user ID for this test
	userID := uuid.New()

	// Initialize user first
	err := suite.service.InitializeUserForActivityCashback(suite.ctx, userID)
	suite.NoError(err)

	// Complete a daily task first
	verificationData := map[string]interface{}{
		"completed_at": time.Now(),
	}
	err = suite.service.CompleteTask(suite.ctx, userID, suite.testTaskID, verificationData)
	suite.NoError(err)

	// Verify task is completed
	progress, err := suite.service.GetTaskProgress(suite.ctx, userID, suite.testTaskID)
	suite.NoError(err)
	suite.Equal(model.TaskStatusCompleted, progress.Status)

	// Reset daily tasks
	err = suite.service.ResetDailyTasks(suite.ctx)
	suite.NoError(err)

	// Verify task was reset
	progress, err = suite.service.GetTaskProgress(suite.ctx, userID, suite.testTaskID)
	suite.NoError(err)
	suite.Equal(model.TaskStatusNotStarted, progress.Status)
}

// TestDashboardData tests dashboard data retrieval
func (suite *ActivityCashbackIntegrationTestSuite) TestDashboardData() {
	// Get user dashboard
	dashboard, err := suite.service.GetUserDashboard(suite.ctx, suite.testUserID)
	suite.NoError(err)
	suite.NotNil(dashboard)
	suite.NotNil(dashboard.UserTierInfo)
	suite.NotNil(dashboard.TierBenefit)
}

// TestTaskCenter tests task center data retrieval
func (suite *ActivityCashbackIntegrationTestSuite) TestTaskCenter() {
	// Get task center
	taskCenter, err := suite.service.GetTaskCenter(suite.ctx, suite.testUserID)
	suite.NoError(err)
	suite.NotNil(taskCenter)
	suite.GreaterOrEqual(len(taskCenter.Categories), 0)
}

// TestCashbackClaim tests cashback claiming functionality
func (suite *ActivityCashbackIntegrationTestSuite) TestCashbackClaim() {
	// Add some claimable cashback
	err := suite.service.AddCashback(suite.ctx, suite.testUserID, decimal.NewFromFloat(10.0))
	suite.NoError(err)

	// Get claimable amount
	claimable, err := suite.service.GetClaimableCashback(suite.ctx, suite.testUserID)
	suite.NoError(err)
	suite.True(claimable.GreaterThan(decimal.Zero))

	// Create a claim
	claim, err := suite.service.CreateClaim(suite.ctx, suite.testUserID, model.ClaimTypeTradingCashback, claimable, decimal.NewFromFloat(0.1), nil)
	suite.NoError(err)
	suite.NotNil(claim)
	suite.Equal(model.ClaimStatusPending, claim.Status)
}

// TestTaskProcessors tests the new task manager system
func (suite *ActivityCashbackIntegrationTestSuite) TestTaskProcessors() {
	// Test task manager (replaces old processors)
	suite.NotNil(suite.processorManager)

	// Test that the task manager can be created independently
	taskManager := NewTaskManager(suite.service)
	suite.NotNil(taskManager)
}

// TestScheduledTasks tests that scheduled tasks are properly configured
func (suite *ActivityCashbackIntegrationTestSuite) TestScheduledTasks() {
	// Test that scheduled task functions exist and can be called
	// This is a placeholder test - in a real scenario you would test the actual scheduled task execution
	suite.T().Log("Scheduled tasks have been migrated to the unified task scheduler system")
	suite.True(true) // Placeholder assertion
}

// TestSystemInitializer tests system initialization
func (suite *ActivityCashbackIntegrationTestSuite) TestSystemInitializer() {
	// Create system initializer
	initializer := NewSystemInitializer()
	suite.NotNil(initializer)

	// Test system status
	status := initializer.GetSystemStatus(suite.ctx)
	suite.NotNil(status)
	suite.Contains(status, "initialized")

	// Test health check
	health := initializer.GetHealthCheck(suite.ctx)
	suite.NotNil(health)
	suite.Contains(health, "status")
}

// TestTaskSeeder tests task seeding functionality
func (suite *ActivityCashbackIntegrationTestSuite) TestTaskSeeder() {
	// Create task seeder
	seeder := NewTaskSeeder()
	suite.NotNil(seeder)

	// Test seeding (this would require proper database setup in real tests)
	// err := seeder.SeedTasks(suite.ctx)
	// suite.NoError(err)
}

// TestAdminOperations tests admin operations
func (suite *ActivityCashbackIntegrationTestSuite) TestAdminOperations() {
	// Test getting all tasks
	tasks, err := suite.adminService.GetAllTasks(suite.ctx)
	suite.NoError(err)
	suite.GreaterOrEqual(len(tasks), 0)

	// Test getting task categories
	categories, err := suite.adminService.GetTaskCategories(suite.ctx)
	suite.NoError(err)
	suite.GreaterOrEqual(len(categories), 0)

	// Test getting tier benefits
	benefits, err := suite.adminService.GetTierBenefits(suite.ctx)
	suite.NoError(err)
	suite.GreaterOrEqual(len(benefits), 0)
}

// TestCreateAccumulatedMEMETradingVolumeTask tests the new dynamic task creation API
func (suite *ActivityCashbackIntegrationTestSuite) TestCreateAccumulatedMEMETradingVolumeTask() {
	// Test creating a new accumulated MEME trading volume task
	volumeThreshold := 2500.0
	points := 750
	adminUserID := uuid.New()

	req := CreateAccumulatedMEMETradingVolumeTaskRequest{
		VolumeThreshold: volumeThreshold,
		Points:          points,
	}

	task, err := suite.adminService.CreateAccumulatedMEMETradingVolumeTask(suite.ctx, req, adminUserID)
	suite.NoError(err)
	suite.NotNil(task)

	// Verify task properties
	suite.Equal("Accumulated MEME Trading $2500", task.Name)
	suite.Equal(points, task.Points)
	suite.Equal(model.FrequencyProgressive, task.Frequency)
	suite.NotNil(task.TaskIdentifier)
	suite.Equal(model.TaskIdentifier("ACCUMULATED_MEME_TRADING_2500"), *task.TaskIdentifier)
	suite.NotNil(task.ActionTarget)
	suite.Equal("memeTrade", *task.ActionTarget)
	suite.NotNil(task.Conditions)
	suite.NotNil(task.Conditions.MinTradingVolume)
	suite.Equal(volumeThreshold, *task.Conditions.MinTradingVolume)

	// Test that the task can be retrieved
	retrievedTask, err := suite.service.GetTaskByID(suite.ctx, task.ID)
	suite.NoError(err)
	suite.Equal(task.ID, retrievedTask.ID)
	suite.Equal(task.Name, retrievedTask.Name)

	// Test that the task appears in the trading category
	tradingTasks, err := suite.service.GetTasksByCategory(suite.ctx, model.CategoryTrading)
	suite.NoError(err)

	found := false
	for _, t := range tradingTasks {
		if t.ID == task.ID {
			found = true
			break
		}
	}
	suite.True(found, "Created task should appear in trading category")
}

// TestErrorHandling tests error handling scenarios
func (suite *ActivityCashbackIntegrationTestSuite) TestErrorHandling() {
	// Test with invalid task ID for task completion
	invalidTaskID := uuid.New() // Non-existent task ID
	userID := uuid.New()

	// Initialize user first
	err := suite.service.InitializeUserForActivityCashback(suite.ctx, userID)
	suite.NoError(err)

	// Try to complete non-existent task
	verificationData := map[string]interface{}{"test": "data"}
	err = suite.service.CompleteTask(suite.ctx, userID, invalidTaskID, verificationData)
	suite.Error(err)

	// Test with nil task ID
	nilTaskID := uuid.Nil
	_, err = suite.service.GetTaskByID(suite.ctx, nilTaskID)
	suite.Error(err)
}

// TestConcurrency tests concurrent operations
func (suite *ActivityCashbackIntegrationTestSuite) TestConcurrency() {
	// Test concurrent task completions
	done := make(chan bool, 10)

	for i := 0; i < 10; i++ {
		go func() {
			defer func() { done <- true }()

			// Try to complete the same task concurrently
			verificationData := map[string]interface{}{
				"completed_at": time.Now(),
			}
			_ = suite.service.CompleteTask(suite.ctx, suite.testUserID, suite.testTaskID, verificationData)
		}()
	}

	// Wait for all goroutines to complete
	for i := 0; i < 10; i++ {
		<-done
	}

	// Verify data consistency
	tierInfo, err := suite.service.GetUserTierInfo(suite.ctx, suite.testUserID)
	suite.NoError(err)
	suite.NotNil(tierInfo)
}

// Run the integration test suite
func TestActivityCashbackIntegrationSuite(t *testing.T) {
	suite.Run(t, new(ActivityCashbackIntegrationTestSuite))
}

// Benchmark tests
func BenchmarkTaskCompletion(b *testing.B) {
	service := NewActivityCashbackService()
	ctx := context.Background()
	userID := uuid.New()
	taskID := uuid.New()

	verificationData := map[string]interface{}{
		"completed_at": time.Now(),
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = service.CompleteTask(ctx, userID, taskID, verificationData)
	}
}

func BenchmarkDashboardRetrieval(b *testing.B) {
	service := NewActivityCashbackService()
	ctx := context.Background()
	userID := uuid.New()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = service.GetUserDashboard(ctx, userID)
	}
}
