package activity_cashback

import (
	"context"
	"fmt"

	"github.com/google/uuid"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"go.uber.org/zap"
)

// TaskManager manages all task processing using the modern registry-based system
// This replaces the legacy TaskProcessorManager with a cleaner, more maintainable architecture
type TaskManager struct {
	service  ActivityCashbackServiceInterface
	registry *TaskRegistry
}

// NewTaskManager creates a new TaskManager
func NewTaskManager(service ActivityCashbackServiceInterface) *TaskManager {
	registry := NewTaskRegistry(service)
	return &TaskManager{
		service:  service,
		registry: registry,
	}
}

// ProcessTask processes a task using the registry system
func (m *TaskManager) ProcessTask(ctx context.Context, userID uuid.UUID, task *model.ActivityTask, data map[string]interface{}) error {
	return m.registry.ProcessTask(ctx, userID, task, data)
}

// ProcessTaskByIdentifier processes a task by its identifier
func (m *TaskManager) ProcessTaskByIdentifier(ctx context.Context, userID uuid.UUID, identifier model.TaskIdentifier, categoryName model.TaskCategoryName, data map[string]interface{}) error {
	return m.registry.ProcessTaskByIdentifier(ctx, userID, identifier, categoryName, data)
}

// ProcessTradingEvent processes trading events and updates relevant tasks
// This is the core business logic that was previously in TaskProcessorManager
func (m *TaskManager) ProcessTradingEvent(ctx context.Context, userID uuid.UUID, tradeData map[string]interface{}) error {
	tradeType, ok := tradeData["trade_type"].(string)
	if !ok {
		return fmt.Errorf("trade type not specified")
	}

	volume, ok := tradeData["volume"].(float64)
	if !ok || volume <= 0 {
		return fmt.Errorf("invalid trade volume")
	}

	global.GVA_LOG.Info("Processing trading event",
		zap.String("user_id", userID.String()),
		zap.String("trade_type", tradeType),
		zap.Float64("volume", volume),
		zap.Any("trade_data", tradeData))

	// Process daily trading tasks and trading points for supported trade types
	switch tradeType {
	case "MEME":
		// Only process MEME trading volume for Activity Cashback volume accumulation
		// Note: Trading volume accumulation is now handled by RealtimeVolumeSyncTask (configurable intervals)
		// which provides real-time updates to user_tier_info.accumulated_volume_usd
		// LevelUpgradeTask still handles daily aggregation to user_tier_info.trading_volume_usd
		global.GVA_LOG.Info("Processing MEME trade event",
			zap.String("user_id", userID.String()),
			zap.String("trade_type", tradeType),
			zap.Float64("volume", volume),
			zap.String("note", "Volume accumulation handled by RealtimeVolumeSyncTask (configurable) and LevelUpgradeTask (daily)"))

		// Process MEME trade task ONLY in daily category (trading points handled separately)
		if err := m.ProcessTaskByIdentifier(ctx, userID, model.TaskIDMemeTradeDaily, "daily", tradeData); err != nil {
			global.GVA_LOG.Error("Failed to process MEME trade task in daily category", zap.Error(err))
		}

	case "PERPETUAL":
		// PERPETUAL trades are processed for daily tasks and trading points
		// but excluded from volume accumulation for Activity Cashback
		global.GVA_LOG.Info("Processing PERPETUAL trade event",
			zap.String("user_id", userID.String()),
			zap.String("trade_type", tradeType),
			zap.Float64("volume", volume),
			zap.String("note", "PERPETUAL trades excluded from volume accumulation but included in Trading Points"))

		// Process PERPETUAL trade task ONLY in daily category (trading points handled separately)
		if err := m.ProcessTaskByIdentifier(ctx, userID, model.TaskIDPerpetualTradeDaily, "daily", tradeData); err != nil {
			global.GVA_LOG.Error("Failed to process PERPETUAL trade task in daily category", zap.Error(err))
		}

	default:
		// Unsupported trade types are skipped
		global.GVA_LOG.Info("Skipping unsupported trade type",
			zap.String("user_id", userID.String()),
			zap.String("trade_type", tradeType),
			zap.Float64("volume", volume))
		return nil
	}

	// Process trading points task for both MEME and PERPETUAL trades
	if err := m.ProcessTaskByIdentifier(ctx, userID, model.TaskIDTradingPoints, "trading", tradeData); err != nil {
		global.GVA_LOG.Error("Failed to process trading points task", zap.Error(err))
	}

	return nil
}

// CheckAndUpdateAllAccumulatedTradingTasks checks all accumulated trading tasks for a user
// This method now works with dynamically created accumulated trading tasks
func (m *TaskManager) CheckAndUpdateAllAccumulatedTradingTasks(ctx context.Context, userID uuid.UUID) error {
	// Get all accumulated trading tasks from database
	// These are tasks with actionTarget = "memeTrade" and frequency = "PROGRESSIVE"
	tasks, err := m.service.GetTasksByActionTargetAndFrequency(ctx, "memeTrade", model.FrequencyProgressive)
	if err != nil {
		return fmt.Errorf("failed to get accumulated trading tasks: %w", err)
	}

	var errors []error
	for _, task := range tasks {
		// Only process tasks that have a task identifier (accumulated trading tasks)
		if task.TaskIdentifier != nil {
			if err := m.ProcessTaskByIdentifier(ctx, userID, *task.TaskIdentifier, "trading", map[string]interface{}{}); err != nil {
				global.GVA_LOG.Warn("Failed to process accumulated trading task",
					zap.String("task_id", string(*task.TaskIdentifier)),
					zap.String("user_id", userID.String()),
					zap.Error(err))
				errors = append(errors, err)
			}
		}
	}

	if len(errors) > 0 {
		return fmt.Errorf("failed to process %d accumulated trading tasks", len(errors))
	}

	return nil
}

// GetRegistry returns the task registry for advanced usage
func (m *TaskManager) GetRegistry() *TaskRegistry {
	return m.registry
}

// GetService returns the activity cashback service
func (m *TaskManager) GetService() ActivityCashbackServiceInterface {
	return m.service
}
