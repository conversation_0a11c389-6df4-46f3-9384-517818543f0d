package invitation

import (
	"context"
	"fmt"
	"math"
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/dto/response"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo/transaction"
	"go.uber.org/zap"
)

// InvitationListServiceInterface defines the interface for invitation list operations
type InvitationListServiceInterface interface {
}

// InvitationListService implements invitation list operations
type InvitationListService struct {
	userRepo        transaction.UserRepositoryInterface
	affiliateRepo   transaction.AffiliateTransactionRepositoryInterface
	hyperLiquidRepo transaction.HyperLiquidTransactionRepositoryInterface
	commissionRepo  transaction.CommissionLedgerRepositoryInterface
}

// NewInvitationListService creates a new invitation list service
func NewInvitationListService() InvitationListServiceInterface {
	return &InvitationListService{
		userRepo:        transaction.NewUserRepository(),
		affiliateRepo:   transaction.NewAffiliateTransactionRepository(),
		hyperLiquidRepo: transaction.NewHyperLiquidTransactionRepository(),
		commissionRepo:  transaction.NewCommissionLedgerRepository(),
	}
}

// GetInvitationList retrieves invitation list with filtering and pagination
func (s *InvitationListService) GetInvitationList(ctx context.Context, userID uuid.UUID, request *response.InvitationListRequest) (*response.InvitationListResponse, error) {
	// If we need address-level expansion (MEME-only or ALL), we must fetch all referrals for correct pagination after expansion
	fetchAll := request.TransactionType == "MEME" || request.TransactionType == "ALL"
	page := request.Page
	pageSize := request.PageSize
	if page < 1 {
		page = 1
	}
	if pageSize < 1 {
		pageSize = 10
	}

	refPage := page
	refPageSize := pageSize
	if fetchAll {
		refPage = 1
		refPageSize = math.MaxInt32 // effectively fetch all to allow correct post-expansion pagination
	}

	referrals, _, err := s.getThreeLevelReferrals(ctx, userID, refPage, refPageSize)
	if err != nil {
		global.GVA_LOG.Error("Failed to get three level referrals", zap.Error(err))
		return nil, fmt.Errorf("failed to get three level referrals: %w", err)
	}

	var invitationItems []*response.InvitationListItem

	for _, referral := range referrals {
		// If ALL is requested, create one entry per concrete type
		if request.TransactionType == "ALL" {
			// MEME: append multiple items grouped by address
			memeItems, err := s.getMemeTransactionDataByAddress(ctx, referral.ID, userID)
			if err != nil {
				global.GVA_LOG.Error("Failed to get MEME items by address",
					zap.String("user_id", referral.ID.String()),
					zap.Error(err))
			} else {
				for _, it := range memeItems {
					if it.TransactionAmount > 0 || it.AccumulatedCommission > 0 {
						// ensure pointer slice
						itemCopy := it
						invitationItems = append(invitationItems, &itemCopy)
					}
				}
			}

			// CONTRACT and SPOT: single items via existing builder
			for _, t := range []string{"CONTRACT", "SPOT"} {
				item, err := s.buildInvitationListItem(ctx, &referral, t, userID)
				if err != nil {
					global.GVA_LOG.Error("Failed to build invitation list item",
						zap.String("user_id", referral.ID.String()),
						zap.Error(err))
					continue
				}
				if item.TransactionAmount > 0 || item.AccumulatedCommission > 0 {
					invitationItems = append(invitationItems, item)
				}
			}
			continue
		}

		// MEME only: return multiple items grouped by address
		if request.TransactionType == "MEME" {
			memeItems, err := s.getMemeTransactionDataByAddress(ctx, referral.ID, userID)
			if err != nil {
				global.GVA_LOG.Error("Failed to get MEME items by address",
					zap.String("user_id", referral.ID.String()),
					zap.Error(err))
				continue
			}
			for _, it := range memeItems {
				if it.TransactionAmount > 0 || it.AccumulatedCommission > 0 {
					itemCopy := it
					invitationItems = append(invitationItems, &itemCopy)
				}
			}
			continue
		}

		item, err := s.buildInvitationListItem(ctx, &referral, request.TransactionType, userID)
		if err != nil {
			global.GVA_LOG.Error("Failed to build invitation list item",
				zap.String("user_id", referral.ID.String()),
				zap.Error(err))
			continue
		}

		// Apply transaction type filter
		if request.TransactionType != "ALL" && item.TransactionType != request.TransactionType {
			continue
		}

		invitationItems = append(invitationItems, item)
	}

	// In-memory pagination on expanded items (address-level for MEME)
	totalCount := len(invitationItems)
	page = request.Page
	pageSize = request.PageSize
	if page < 1 {
		page = 1
	}
	if pageSize < 1 {
		pageSize = 10
	}
	start := (page - 1) * pageSize
	if start > totalCount {
		start = totalCount
	}
	end := start + pageSize
	if end > totalCount {
		end = totalCount
	}
	paged := invitationItems[start:end]

	return &response.InvitationListResponse{
		Data:     paged,
		Total:    totalCount,
		Page:     page,
		PageSize: pageSize,
		Success:  true,
		Message:  "Successfully obtained the invitation list",
	}, nil
}

// buildInvitationListItem builds a single invitation list item
func (s *InvitationListService) buildInvitationListItem(ctx context.Context, user *model.User, transactionType string, userRefferID uuid.UUID) (*response.InvitationListItem, error) {
	// Get user wallet address
	walletAddress, err := s.getUserWalletAddress(ctx, user.ID)
	if err != nil {
		return nil, fmt.Errorf("failed to get wallet address: %w", err)
	}

	// Get invitation time from referral record
	invitationTime, err := s.getInvitationTime(ctx, user.ID)
	if err != nil {
		return nil, fmt.Errorf("failed to get invitation time: %w", err)
	}

	// Format date as "MM-DD"
	date := invitationTime.Format("01-02")

	// Get transaction data based on type
	var transactionTypeStr string
	var transactionAmount float64
	var accumulatedCommission float64

	switch transactionType {
	case "MEME":
		transactionTypeStr = "MEME"
		amount, commission, err := s.getMemeTransactionData(ctx, user.ID)
		if err != nil {
			global.GVA_LOG.Warn("Failed to get MEME transaction data", zap.Error(err))
		} else {
			transactionAmount = amount
			accumulatedCommission = commission
		}
	case "CONTRACT":
		transactionTypeStr = "CONTRACT"
		amount, commission, err := s.getContractTransactionData(ctx, user.ID, userRefferID)
		if err != nil {
			global.GVA_LOG.Warn("Failed to get contract transaction data", zap.Error(err))
		} else {
			transactionAmount = amount
			accumulatedCommission = commission
		}
	case "SPOT":
		transactionTypeStr = "SPOT"
		amount, commission, err := s.getSpotTransactionData(ctx, user.ID)
		if err != nil {
			global.GVA_LOG.Warn("Failed to get spot transaction data", zap.Error(err))
		} else {
			transactionAmount = amount
			accumulatedCommission = commission
		}
	default:
		// For "ALL" type, get the highest transaction amount and commission
		transactionTypeStr, transactionAmount, accumulatedCommission = s.getBestTransactionData(ctx, user.ID, userRefferID)
	}

	return &response.InvitationListItem{
		UserAddress:           walletAddress,
		InvitationTime:        invitationTime,
		TransactionType:       transactionTypeStr,
		TransactionAmount:     transactionAmount,
		AccumulatedCommission: accumulatedCommission,
		Date:                  date,
	}, nil
}

// getUserWalletAddress gets the wallet address for a user
func (s *InvitationListService) getUserWalletAddress(ctx context.Context, userID uuid.UUID) (string, error) {
	var walletAddress string
	err := global.GVA_DB.WithContext(ctx).
		Model(&model.UserWallet{}).
		Select("wallet_address").
		Where("user_id = ?", userID).
		Limit(1).
		Pluck("wallet_address", &walletAddress).Error

	if err != nil {
		return "", err
	}

	if walletAddress == "" {
		return "Unbound wallet", nil
	}

	return walletAddress, nil
}

// getUserWalletAddresses gets all wallet addresses for a user (meme有多个地址)
func (s *InvitationListService) getUserWalletAddresses(ctx context.Context, userID uuid.UUID) ([]string, error) {
	var walletAddresses []string
	err := global.GVA_DB.WithContext(ctx).
		Model(&model.UserWallet{}).
		Select("wallet_address").
		Where("user_id = ? AND wallet_address != ''", userID).
		Pluck("wallet_address", &walletAddresses).Error

	if err != nil {
		return nil, err
	}

	return walletAddresses, nil
}

// getInvitationTime gets the invitation time from referral record
func (s *InvitationListService) getInvitationTime(ctx context.Context, userID uuid.UUID) (time.Time, error) {
	var referral model.Referral
	err := global.GVA_DB.WithContext(ctx).
		Where("user_id = ? AND depth = 1", userID).
		First(&referral).Error

	if err != nil {
		return time.Time{}, err
	}

	return referral.CreatedAt, nil
}

// getThreeLevelReferrals gets referrals up to level 3 with their wallet addresses
func (s *InvitationListService) getThreeLevelReferrals(ctx context.Context, userID uuid.UUID, page, pageSize int) ([]model.User, int64, error) {
	var referrals []model.User
	var total int64

	err := global.GVA_DB.WithContext(ctx).
		Model(&model.User{}).
		Joins("JOIN referrals ON users.id = referrals.user_id").
		Where("referrals.referrer_id = ? AND referrals.depth <= 3", userID).
		Distinct("users.id").
		Count(&total).Error

	if err != nil {
		return nil, 0, err
	}

	offset := (page - 1) * pageSize
	err = global.GVA_DB.WithContext(ctx).
		Model(&model.User{}).
		Joins("JOIN referrals ON users.id = referrals.user_id").
		Joins("LEFT JOIN user_wallets ON users.id = user_wallets.user_id").
		Where("referrals.referrer_id = ? AND referrals.depth <= 3", userID).
		Group("users.id, users.created_at, users.updated_at, users.deleted_at, referrals.created_at").
		Order("referrals.created_at DESC").
		Offset(offset).
		Limit(pageSize).
		Find(&referrals).Error

	if err != nil {
		return nil, 0, err
	}

	return referrals, total, nil
}

// getMemeTransactionData gets MEME transaction data for a user
// 计算meme地址交易量 sum(AffiliateTransaction.QuoteAmount * SolPriceSnapshot.price)
// 计算meme地址累计返佣 sum(meme_commission_ledger.commission_amount)
func (s *InvitationListService) getMemeTransactionData(ctx context.Context, userID uuid.UUID) (float64, float64, error) {
	// Get user wallet addresses
	walletAddresses, err := s.getUserWalletAddresses(ctx, userID)
	if err != nil {
		return 0, 0, fmt.Errorf("failed to get wallet addresses: %w", err)
	}

	if len(walletAddresses) == 0 {
		return 0, 0, nil
	}

	// Get transaction volume: sum(AffiliateTransaction.QuoteAmount * SolPriceSnapshot.price)
	var result struct {
		TotalVolume decimal.Decimal `json:"total_volume"`
	}

	err = global.GVA_DB.WithContext(ctx).
		Model(&model.AffiliateTransaction{}).
		Joins("JOIN sol_price_snapshots ON DATE(affiliate_transactions.created_at) = DATE(sol_price_snapshots.timestamp)").
		Select("COALESCE(SUM(affiliate_transactions.quote_amount * sol_price_snapshots.price), 0) as total_volume").
		Where("affiliate_transactions.user_address IN ? AND affiliate_transactions.status = ?",
			walletAddresses, "Completed").
		Scan(&result).Error

	if err != nil {
		return 0, 0, err
	}

	// Get commission amount: sum(meme_commission_ledger.commission_amount)
	var commissionResult struct {
		TotalCommission decimal.Decimal `json:"total_commission"`
	}

	err = global.GVA_DB.WithContext(ctx).
		Model(&model.CommissionLedger{}).
		Select("COALESCE(SUM(commission_amount), 0) as total_commission").
		Where("source_user_id = ? AND source_transaction_type = ?", userID, "MEME").
		Scan(&commissionResult).Error

	if err != nil {
		global.GVA_LOG.Warn("Failed to get MEME commission amount", zap.Error(err))
	}

	volume, _ := result.TotalVolume.Float64()
	commission, _ := commissionResult.TotalCommission.Float64()

	return volume, commission, nil
}

// 使用 LATERAL 子查询：对每笔成交，取其 created_at 当时点及之前的最近一条 SOL 价格
// 汇总维度：affiliate_transactions.user_address
// 累计返佣来源：meme_commission_ledger（按用户维度，使用 source_user_id 聚合）
func (s *InvitationListService) getMemeTransactionDataByAddress(ctx context.Context, userID uuid.UUID, userRefferID uuid.UUID) ([]response.InvitationListItem, error) {
	// 读取用户绑定的钱包地址
	walletAddresses, err := s.getUserWalletAddresses(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get wallet addresses: %w", err)
	}
	if len(walletAddresses) == 0 {
		return []response.InvitationListItem{}, nil
	}

	// 拉取邀请时间、格式化日期（与现有口径一致）
	invitationTime, err := s.getInvitationTime(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get invitation time: %w", err)
	}
	date := invitationTime.Format("01-02")

	// 用户维度累计返佣：改读 meme_commission_ledger
	var commissionResult struct {
		TotalCommission decimal.Decimal `json:"total_commission"`
	}
	_ = global.GVA_DB.WithContext(ctx).
		Model(&model.MemeCommissionLedger{}).
		Select("COALESCE(SUM(commission_amount), 0) AS total_commission").
		Where("recipient_user_id = ? AND source_user_id = ?", userRefferID, userID).
		Scan(&commissionResult).Error
	commissionTotal, _ := commissionResult.TotalCommission.Float64()

	// 按地址分组统计 MEME 成交额（USD），使用 LATERAL 子查询获取每笔成交时点最近价格
	type row struct {
		UserAddress string          `json:"user_address"`
		TotalVolume decimal.Decimal `json:"total_volume"`
	}
	var rows []row

	sql := `
		SELECT
			at.user_address,
			COALESCE(SUM(at.quote_amount * COALESCE(sps.price, 0)), 0) AS total_volume
		FROM affiliate_transactions at
		LEFT JOIN LATERAL (
			SELECT price
			FROM sol_price_snapshots
			WHERE timestamp <= at.created_at
			ORDER BY timestamp DESC
			LIMIT 1
		) sps ON true
		WHERE
			at.user_address IN (?) 
			AND at.status = 'Completed'
			AND at.deleted_at IS NULL
		GROUP BY at.user_address
	`

	if err := global.GVA_DB.WithContext(ctx).
		Raw(sql, walletAddresses).
		Scan(&rows).Error; err != nil {
		return nil, err
	}

	items := make([]response.InvitationListItem, 0, len(rows))
	for _, r := range rows {
		vol, _ := r.TotalVolume.Float64()
		// 仅返回有实际数据的地址（若需要全部地址含 0 值，可移除此判断）
		if vol == 0 && commissionTotal == 0 {
			continue
		}
		items = append(items, response.InvitationListItem{
			UserAddress:           r.UserAddress,
			InvitationTime:        invitationTime,
			TransactionType:       "MEME",
			TransactionAmount:     vol,
			AccumulatedCommission: commissionTotal,
			Date:                  date,
		})
	}

	return items, nil
}

// getContractTransactionData gets contract transaction data for a user
// 计算合约地址交易量 sum(HyperLiquidTransaction.total_sz * avg_price)
// 计算合约地址累计返佣 sum(commission_ledger.commission_amount)
func (s *InvitationListService) getContractTransactionData(ctx context.Context, userID uuid.UUID, userRefferID uuid.UUID) (float64, float64, error) {
	// Get user wallet addresses
	walletAddresses, err := s.getUserWalletAddresses(ctx, userID)
	if err != nil {
		return 0, 0, fmt.Errorf("failed to get wallet addresses: %w", err)
	}

	if len(walletAddresses) == 0 {
		return 0, 0, nil
	}

	// Get transaction volume: sum(HyperLiquidTransaction.total_sz * avg_price)
	var result struct {
		TotalVolume decimal.Decimal `json:"total_volume"`
	}

	err = global.GVA_DB.WithContext(ctx).
		Model(&model.HyperLiquidTransaction{}).
		Select("COALESCE(SUM(total_sz::decimal * avg_price), 0) as total_volume").
		Where("wallet_address IN ? AND status = ?", walletAddresses, "filled").
		Scan(&result).Error

	if err != nil {
		return 0, 0, err
	}

	// Get commission amount: sum(commission_ledger.commission_amount)
	var commissionResult struct {
		TotalCommission decimal.Decimal `json:"total_commission"`
	}

	err = global.GVA_DB.WithContext(ctx).
		Model(&model.CommissionLedger{}).
		Select("COALESCE(SUM(commission_amount), 0) as total_commission").
		// Where("source_user_id = ? AND source_transaction_type = ? AND status = ?",
		// 	userID, "CONTRACT", "CLAIMED").
		Where("recipient_user_id = ? AND source_user_id = ? ", userRefferID, userID).
		Scan(&commissionResult).Error

	if err != nil {
		global.GVA_LOG.Warn("Failed to get contract commission amount", zap.Error(err))
	}

	volume, _ := result.TotalVolume.Float64()
	commission, _ := commissionResult.TotalCommission.Float64()

	return volume, commission, nil
}

// getSpotTransactionData gets spot transaction data for a user (placeholder for future implementation)
func (s *InvitationListService) getSpotTransactionData(ctx context.Context, userID uuid.UUID) (float64, float64, error) {
	// TODO: Implement spot transaction data retrieval
	return 0, 0, nil
}

// getMemeAddressesWithActivity returns distinct MEME transaction addresses for the user
func (s *InvitationListService) getMemeAddressesWithActivity(ctx context.Context, userID uuid.UUID) ([]string, error) { /* removed */
	// Limit to this user's known wallet addresses to avoid cross-user leakage
	walletAddresses, err := s.getUserWalletAddresses(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get wallet addresses: %w", err)
	}
	if len(walletAddresses) == 0 {
		return []string{}, nil
	}

	var addresses []string
	err = global.GVA_DB.WithContext(ctx).
		Model(&model.AffiliateTransaction{}).
		Select("DISTINCT user_address").
		Where("user_address IN ? AND transaction_type = ? AND status = ?", walletAddresses, "MEME", "Completed").
		Pluck("user_address", &addresses).Error

	if err != nil {
		return nil, err
	}
	return addresses, nil
}

// getMemeVolumeByAddress returns MEME交易量（USD）按单一地址汇总
func (s *InvitationListService) getMemeVolumeByAddress(ctx context.Context, address string) (float64, error) { /* removed */
	var result struct {
		TotalVolume decimal.Decimal `json:"total_volume"`
	}

	err := global.GVA_DB.WithContext(ctx).
		Model(&model.AffiliateTransaction{}).
		Joins("JOIN sol_price_snapshots ON DATE(affiliate_transactions.created_at) = DATE(sol_price_snapshots.timestamp)").
		Select("COALESCE(SUM(affiliate_transactions.quote_amount * sol_price_snapshots.price), 0) as total_volume").
		Where("affiliate_transactions.user_address = ? AND affiliate_transactions.transaction_type = ? AND affiliate_transactions.status = ?",
			address, "MEME", "Completed").
		Scan(&result).Error

	if err != nil {
		return 0, err
	}

	volume, _ := result.TotalVolume.Float64()
	return volume, nil
}

// getMemeCommissionByUser returns 用户维度MEME累计返佣
func (s *InvitationListService) getMemeCommissionByUser(ctx context.Context, userID uuid.UUID) (float64, error) { /* removed */
	var commissionResult struct {
		TotalCommission decimal.Decimal `json:"total_commission"`
	}

	err := global.GVA_DB.WithContext(ctx).
		Model(&model.CommissionLedger{}).
		Select("COALESCE(SUM(commission_amount), 0) as total_commission").
		Where("source_user_id = ? AND source_transaction_type = ?", userID, "MEME").
		Scan(&commissionResult).Error

	if err != nil {
		return 0, err
	}
	commission, _ := commissionResult.TotalCommission.Float64()
	return commission, nil
}

// getBestTransactionData gets the best transaction data across all types
func (s *InvitationListService) getBestTransactionData(ctx context.Context, userID uuid.UUID, userRefferID uuid.UUID) (string, float64, float64) {
	memeVolume, memeCommission, _ := s.getMemeTransactionData(ctx, userID)
	contractVolume, contractCommission, _ := s.getContractTransactionData(ctx, userID, userRefferID)
	spotVolume, spotCommission, _ := s.getSpotTransactionData(ctx, userID)

	// Find the type with highest volume
	if memeVolume >= contractVolume && memeVolume >= spotVolume {
		return "MEME", memeVolume, memeCommission
	} else if contractVolume >= spotVolume {
		return "CONTRACT", contractVolume, contractCommission
	} else {
		return "SPOT", spotVolume, spotCommission
	}
}

// sortByInvitationTime sorts items by invitation time (newest first)
func (s *InvitationListService) sortByInvitationTime(items []*response.InvitationListItem) {
	for i := 0; i < len(items)-1; i++ {
		for j := 0; j < len(items)-i-1; j++ {
			if items[j].InvitationTime.Before(items[j+1].InvitationTime) {
				items[j], items[j+1] = items[j+1], items[j]
			}
		}
	}
}
