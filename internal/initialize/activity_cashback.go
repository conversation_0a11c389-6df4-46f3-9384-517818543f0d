package initialize

import (
	"context"
	"fmt"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/shopspring/decimal"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/middleware"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/service/activity_cashback"
	"go.uber.org/zap"
)

// InitializeActivityCashbackSystem initializes the Activity Cashback System
func InitializeActivityCashbackSystem() error {
	global.GVA_LOG.Info("Initializing Activity Cashback System...")

	// Create context with timeout for initialization
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// Initialize the global system
	if err := activity_cashback.InitializeGlobalSystem(ctx); err != nil {
		global.GVA_LOG.Error("Failed to initialize Activity Cashback System", zap.Error(err))
		return err
	}

	global.GVA_LOG.Info("Activity Cashback System initialized successfully")
	return nil
}

// SetupActivityCashbackRoutes sets up routes for Activity Cashback System
func SetupActivityCashbackRoutes(router *gin.Engine) {
	global.GVA_LOG.Info("Setting up Activity Cashback routes...")

	// Create middleware instance
	activityMiddleware := middleware.NewActivityCashbackMiddleware()

	// Register routes
	activityMiddleware.RegisterRoutes(router)

	// Add activity tracking middleware to all routes
	router.Use(activityMiddleware.TrackUserActivity())

	global.GVA_LOG.Info("Activity Cashback routes set up successfully")
}

// ShutdownActivityCashbackSystem gracefully shuts down the Activity Cashback System
func ShutdownActivityCashbackSystem() {
	global.GVA_LOG.Info("Shutting down Activity Cashback System...")

	// Shutdown the global system
	activity_cashback.ShutdownGlobalSystem()

	global.GVA_LOG.Info("Activity Cashback System shutdown completed")
}

// SeedActivityCashbackData seeds initial data for Activity Cashback System
func SeedActivityCashbackData() error {
	global.GVA_LOG.Info("Seeding Activity Cashback initial data...")

	ctx, cancel := context.WithTimeout(context.Background(), 60*time.Second)
	defer cancel()

	// Create admin service
	adminService := activity_cashback.NewAdminService()

	// Seed task categories
	if err := seedTaskCategories(ctx, adminService); err != nil {
		global.GVA_LOG.Error("Failed to seed task categories", zap.Error(err))
		return err
	}

	// Seed tier benefits
	if err := seedTierBenefits(ctx, adminService); err != nil {
		global.GVA_LOG.Error("Failed to seed tier benefits", zap.Error(err))
		return err
	}

	// Seed initial tasks (respects ENABLE_TASK_SEEDER configuration)
	if err := adminService.SeedInitialTasks(ctx); err != nil {
		global.GVA_LOG.Error("Failed to seed initial tasks", zap.Error(err))
		return err
	}

	global.GVA_LOG.Info("Activity Cashback initial data seeded successfully")
	return nil
}

// seedTaskCategories seeds initial task categories
func seedTaskCategories(ctx context.Context, adminService activity_cashback.AdminServiceInterface) error {
	categories := []struct {
		name        model.TaskCategoryName
		displayName string
		description string
		icon        string
		sortOrder   int
	}{
		{
			name:        model.CategoryDaily,
			displayName: model.CategoryDaily.GetDisplayName(),
			description: model.CategoryDaily.GetDescription(),
			icon:        model.CategoryDaily.GetIcon(),
			sortOrder:   model.CategoryDaily.GetSortOrder(),
		},
		{
			name:        model.CategoryCommunity,
			displayName: model.CategoryCommunity.GetDisplayName(),
			description: model.CategoryCommunity.GetDescription(),
			icon:        model.CategoryCommunity.GetIcon(),
			sortOrder:   model.CategoryCommunity.GetSortOrder(),
		},
		{
			name:        model.CategoryTrading,
			displayName: model.CategoryTrading.GetDisplayName(),
			description: model.CategoryTrading.GetDescription(),
			icon:        model.CategoryTrading.GetIcon(),
			sortOrder:   model.CategoryTrading.GetSortOrder(),
		},
	}

	for _, cat := range categories {
		// Check if category already exists
		existingCategories, err := adminService.GetTaskCategories(ctx)
		if err != nil {
			return err
		}

		exists := false
		for _, existing := range existingCategories {
			if existing.Name == cat.name {
				exists = true
				break
			}
		}

		if !exists {
			category := &model.TaskCategory{
				Name:        cat.name,
				DisplayName: cat.displayName,
				Description: &cat.description,
				Icon:        &cat.icon,
				IsActive:    true,
				SortOrder:   cat.sortOrder,
			}

			if err := adminService.CreateTaskCategory(ctx, category); err != nil {
				return err
			}

			global.GVA_LOG.Info("Task category created", zap.String("name", string(cat.name)))
		}
	}

	return nil
}

// seedTierBenefits seeds initial tier benefits
func seedTierBenefits(ctx context.Context, adminService activity_cashback.AdminServiceInterface) error {
	benefits := []struct {
		tierLevel                   int
		tierName                    string
		minPoints                   int
		cashbackPercentage          float64
		benefitsDescription         string
		tierColor                   string
		tierIcon                    string
		netFee                      float64
		referredIncentivePercentage float64
	}{
		{
			tierLevel:                   1,
			tierName:                    "Bronze",
			minPoints:                   0,
			cashbackPercentage:          0.0000, // 0% cashback
			benefitsDescription:         "Basic tier with 0% cashback on all trades",
			tierColor:                   "#CD7F32",
			tierIcon:                    "bronze-medal",
			netFee:                      0.0095, // 0.95% net fee
			referredIncentivePercentage: 0.0500, // 5% referred incentive
		},
		{
			tierLevel:                   2,
			tierName:                    "Silver",
			minPoints:                   1000,
			cashbackPercentage:          0.0500, // 5% cashback
			benefitsDescription:         "Silver tier with 5% cashback on all trades",
			tierColor:                   "#C0C0C0",
			tierIcon:                    "silver-medal",
			netFee:                      0.0090, // 0.90% net fee
			referredIncentivePercentage: 0.0500, // 5% referred incentive
		},
		{
			tierLevel:                   3,
			tierName:                    "Gold",
			minPoints:                   5000,
			cashbackPercentage:          0.1000, // 10% cashback
			benefitsDescription:         "Gold tier with 10% cashback on all trades",
			tierColor:                   "#FFD700",
			tierIcon:                    "gold-medal",
			netFee:                      0.0085, // 0.85% net fee
			referredIncentivePercentage: 0.0500, // 5% referred incentive
		},
		{
			tierLevel:                   4,
			tierName:                    "Platinum",
			minPoints:                   15000,
			cashbackPercentage:          0.1500, // 15% cashback
			benefitsDescription:         "Platinum tier with 15% cashback on all trades",
			tierColor:                   "#E5E4E2",
			tierIcon:                    "platinum-medal",
			netFee:                      0.0080, // 0.80% net fee
			referredIncentivePercentage: 0.0500, // 5% referred incentive
		},
		{
			tierLevel:                   5,
			tierName:                    "Diamond",
			minPoints:                   50000,
			cashbackPercentage:          0.2000, // 20% cashback
			benefitsDescription:         "Diamond tier with 20% cashback on all trades",
			tierColor:                   "#B9F2FF",
			tierIcon:                    "diamond-medal",
			netFee:                      0.0075, // 0.75% net fee
			referredIncentivePercentage: 0.0500, // 5% referred incentive
		},
	}

	for _, benefit := range benefits {
		// Check if tier benefit already exists
		existingBenefits, err := adminService.GetTierBenefits(ctx)
		if err != nil {
			return err
		}

		exists := false
		for _, existing := range existingBenefits {
			if existing.TierLevel == benefit.tierLevel {
				exists = true
				break
			}
		}

		if !exists {
			tierBenefit := &model.TierBenefit{
				TierLevel:                   benefit.tierLevel,
				TierName:                    benefit.tierName,
				MinPoints:                   benefit.minPoints,
				CashbackPercentage:          decimal.NewFromFloat(benefit.cashbackPercentage),
				ReferredIncentivePercentage: decimal.NewFromFloat(benefit.referredIncentivePercentage),
				NetFee:                      decimal.NewFromFloat(benefit.netFee),
				BenefitsDescription:         &benefit.benefitsDescription,
				TierColor:                   &benefit.tierColor,
				TierIcon:                    &benefit.tierIcon,
				IsActive:                    true,
			}

			if err := adminService.CreateTierBenefit(ctx, tierBenefit); err != nil {
				return err
			}

			global.GVA_LOG.Info("Tier benefit created",
				zap.Int("tier_level", benefit.tierLevel),
				zap.String("tier_name", benefit.tierName),
				zap.Float64("cashback_percentage", benefit.cashbackPercentage),
				zap.Float64("net_fee", benefit.netFee))
		}
	}

	return nil
}

// ProcessExternalEvent processes events from external systems
func ProcessExternalEvent(eventType string, userID string, data map[string]interface{}) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	if err := activity_cashback.ProcessGlobalEvent(ctx, eventType, userID, data); err != nil {
		global.GVA_LOG.Error("Failed to process external event",
			zap.Error(err),
			zap.String("event_type", eventType),
			zap.String("user_id", userID))
	}
}

// GetSystemStatus returns the current system status
func GetActivityCashbackSystemStatus() map[string]interface{} {
	initializer := activity_cashback.GetGlobalSystemInitializer()
	if initializer == nil {
		return map[string]interface{}{
			"status": "not_initialized",
		}
	}

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	return initializer.GetSystemStatus(ctx)
}

// GetSystemHealth returns the system health check
func GetActivityCashbackSystemHealth() map[string]interface{} {
	initializer := activity_cashback.GetGlobalSystemInitializer()
	if initializer == nil {
		return map[string]interface{}{
			"status": "not_initialized",
		}
	}

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	return initializer.GetHealthCheck(ctx)
}

// ForceTaskReset forces a task reset (admin function)
func ForceActivityCashbackTaskReset(resetType string) error {
	initializer := activity_cashback.GetGlobalSystemInitializer()
	if initializer == nil {
		return fmt.Errorf("system not initialized")
	}

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	return initializer.ForceTaskReset(ctx, resetType)
}

// RecalculateAllTiers recalculates all user tiers (admin function)
func RecalculateAllActivityCashbackTiers() error {
	initializer := activity_cashback.GetGlobalSystemInitializer()
	if initializer == nil {
		return fmt.Errorf("system not initialized")
	}

	ctx, cancel := context.WithTimeout(context.Background(), 60*time.Second)
	defer cancel()

	return initializer.RecalculateAllTiers(ctx)
}
