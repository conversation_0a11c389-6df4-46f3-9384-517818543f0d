package task

import (
	"context"
	"encoding/json"

	"github.com/nats-io/nats.go"
	"go.uber.org/zap"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	natsClient "gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/nats"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/service/affiliate"
)

// ConsumeMemeAffiliateTxEvent processes affiliate transaction messages from NATS
func ConsumeMemeAffiliateTxEvent(msgs []*nats.Msg) error {
	logger := global.GVA_LOG

	// Initialize affiliate service
	affiliateService := affiliate.NewAffiliateService()
	ctx := context.Background()

	for _, m := range msgs {
		// Log raw message for debugging
		dataPreview := string(m.Data)
		if len(dataPreview) > 500 {
			dataPreview = dataPreview[:500] + "..."
		}
		logger.Debug("Received raw affiliate transaction message",
			zap.String("subject", m.Subject),
			zap.Int("data_size", len(m.Data)),
			zap.String("data_preview", dataPreview))

		var wrapper natsClient.AffiliateTxEventWrapper
		if err := json.Unmarshal(m.Data, &wrapper); err != nil {
			logger.Error("Failed to unmarshal affiliate transaction message",
				zap.Error(err),
				zap.String("data", string(m.Data)))
			continue
		}

		logger.Debug("Parsed affiliate transaction wrapper",
			zap.Int("event_count", len(wrapper.Items)))

		// Process each transaction in the batch
		for _, txEvent := range wrapper.Items {
			logger.Debug("Processing affiliate transaction event",
				zap.String("order_id", txEvent.ID.String()),
				zap.String("user_id", txEvent.UserId),
				zap.String("status", string(txEvent.Status)),
				zap.String("transaction_type", string(txEvent.TransactionType)))

			// Skip processing if essential fields are missing
			if txEvent.UserId == "" {
				logger.Warn("Skipping transaction with empty user_id",
					zap.String("order_id", txEvent.ID.String()))
				continue
			}

			// Process the transaction
			if err := affiliateService.ProcessAffiliateTransaction(ctx, &txEvent); err != nil {
				logger.Error("Failed to process affiliate transaction",
					zap.Error(err),
					zap.String("order_id", txEvent.ID.String()),
					zap.String("user_id", txEvent.UserId))
				// Continue processing other transactions in the batch
				continue
			}

			logger.Debug("Successfully processed affiliate transaction",
				zap.String("order_id", txEvent.ID.String()),
				zap.String("user_id", txEvent.UserId))
		}
	}

	return nil
}

// ConsumeMemeSolPriceEvent processes SOL price update messages from NATS
func ConsumeMemeSolPriceEvent(msgs []*nats.Msg) error {
	logger := global.GVA_LOG

	// Initialize affiliate service
	affiliateService := affiliate.NewAffiliateService()
	ctx := context.Background()

	for _, m := range msgs {
		var priceEvent natsClient.SolPriceEvent
		if err := json.Unmarshal(m.Data, &priceEvent); err != nil {
			logger.Error("Failed to unmarshal SOL price message",
				zap.Error(err),
				zap.String("data", string(m.Data)))
			continue
		}

		logger.Debug("Received SOL price update",
			zap.String("symbol", priceEvent.GetSymbol()),
			zap.String("price", priceEvent.UsdPrice.String()),
			zap.Time("timestamp", priceEvent.GetTime()))

		// Process the price update
		if err := affiliateService.ProcessSolPriceUpdate(ctx, &priceEvent); err != nil {
			logger.Error("Failed to process SOL price update",
				zap.Error(err),
				zap.String("symbol", priceEvent.GetSymbol()))
			// Continue processing other price updates in the batch
			continue
		}

		logger.Debug("Successfully processed SOL price update",
			zap.String("symbol", priceEvent.GetSymbol()),
			zap.String("price", priceEvent.UsdPrice.String()))
	}

	return nil
}
