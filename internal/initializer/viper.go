package initializer

import (
	"bytes"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"text/template"

	"github.com/fsnotify/fsnotify"
	"github.com/joho/godotenv"
	"github.com/spf13/viper"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
)

func Viper(path ...string) *viper.Viper {
	var config string

	if len(path) == 0 {
		flag := "config.yaml"
		config = flag
		if configEnv := os.Getenv("GVA_CONFIG"); configEnv != "" {
			config = configEnv
			fmt.Printf("Using GVA_CONFIG environment variable, config path: %v\n", config)
		} else {
			fmt.Printf("Using default config value, config path: %v\n", config)
		}
	} else {
		config = path[0]
		fmt.Printf("Using command line value, config path: %v\n", config)
	}

	//Get STAGE variable to select the .env file
	var stage string
	if stage = os.Getenv("STAGE"); stage == "" {
		stage = "local" // Default stage if not explicitly set
		fmt.Printf("STAGE environment variable not set, defaulting to '%s'.\n", stage)
		// Optionally, set the STAGE env var here so it's available for godotenv.
		os.Setenv("STAGE", stage)
	} else {
		fmt.Printf("STAGE environment variable set to '%s'.\n", stage)
	}
	envFilePath := filepath.Join("env", fmt.Sprintf("%s.env", stage))
	err := godotenv.Load(envFilePath)
	if err != nil {
		fmt.Printf("Warning: Could not load .env file %s. Error: %v. Proceeding without it.\n", envFilePath, err)
	} else {
		fmt.Printf("Successfully loaded environment variables from: %s\n", envFilePath)
	}

	// re-generate yaml template from env
	envVars := getEnvMap()

	// Define template functions
	funcMap := template.FuncMap{
		"default": func(defaultValue string, value interface{}) string {
			if value == nil || value == "" {
				return defaultValue
			}
			return fmt.Sprintf("%v", value)
		},
		"ne": func(a, b interface{}) bool {
			return fmt.Sprintf("%v", a) != fmt.Sprintf("%v", b)
		},
		"index": func(m map[string]string, key string) interface{} {
			if val, ok := m[key]; ok {
				return val
			}
			return ""
		},
	}

	tmpl, err := template.New("config.yaml").Funcs(funcMap).ParseFiles(config)
	if err != nil {
		panic(fmt.Errorf("Fatal error parse config: %s \n", err))
	}

	var buf bytes.Buffer
	err = tmpl.Execute(&buf, envVars)
	if err != nil {
		panic(fmt.Errorf("Fatal error parse config with env: %s \n", err))
	}

	renderedYaml := buf.String()

	v := viper.New()
	v.SetConfigType("yaml")
	err = v.ReadConfig(strings.NewReader(renderedYaml))
	if err != nil {
		panic(fmt.Errorf("Fatal error config file: %s \n", err))
	}
	v.WatchConfig()

	v.OnConfigChange(func(e fsnotify.Event) {
		fmt.Println("config file changed:", e.Name)
		if err = v.Unmarshal(&global.GVA_CONFIG); err != nil {
			fmt.Println(err)
		}
	})

	if err = v.Unmarshal(&global.GVA_CONFIG); err != nil {
		panic(err)
	}

	// Root path adaptability
	// Find the corresponding migration location based on root position to ensure root path is valid
	global.GVA_CONFIG.Autocode.Root, _ = os.Getwd()

	// Only log if logger is initialized (avoid nil pointer dereference)
	if global.GVA_LOG != nil {
		global.GVA_LOG.Info("config file loaded successfully")
	} else {
		fmt.Println("config file loaded successfully")
	}
	return v
}

func getEnvMap() map[string]string {
	envMap := make(map[string]string)
	for _, env := range os.Environ() {
		parts := strings.SplitN(env, "=", 2)
		if len(parts) == 2 {
			envMap[parts[0]] = parts[1]
		}
	}
	return envMap
}
