package activity_cashback

import (
	"context"
	"time"

	"github.com/google/uuid"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gorm.io/gorm"
)

// ActivityTaskRepository implements ActivityTaskRepositoryInterface
type ActivityTaskRepository struct {
	db *gorm.DB
}

// NewActivityTaskRepository creates a new ActivityTaskRepository
func NewActivityTaskRepository() ActivityTaskRepositoryInterface {
	return &ActivityTaskRepository{
		db: global.GVA_DB,
	}
}

// Create creates a new activity task
func (r *ActivityTaskRepository) Create(ctx context.Context, task *model.ActivityTask) error {
	return r.db.WithContext(ctx).Create(task).Error
}

// Update updates an existing activity task
func (r *ActivityTaskRepository) Update(ctx context.Context, task *model.ActivityTask) error {
	return r.db.WithContext(ctx).Save(task).Error
}

// Delete soft deletes an activity task
func (r *ActivityTaskRepository) Delete(ctx context.Context, id uuid.UUID) error {
	return r.db.WithContext(ctx).Model(&model.ActivityTask{}).
		Where("id = ?", id).
		Update("is_active", false).Error
}

// GetByID retrieves an activity task by ID
func (r *ActivityTaskRepository) GetByID(ctx context.Context, id uuid.UUID) (*model.ActivityTask, error) {
	var task model.ActivityTask
	err := r.db.WithContext(ctx).
		Preload("Category").
		First(&task, "id = ?", id).Error
	if err != nil {
		return nil, err
	}
	return &task, nil
}

// GetByTaskIdentifier retrieves an activity task by task identifier
func (r *ActivityTaskRepository) GetByTaskIdentifier(ctx context.Context, identifier model.TaskIdentifier) (*model.ActivityTask, error) {
	var task model.ActivityTask
	err := r.db.WithContext(ctx).
		Preload("Category").
		Where("task_identifier = ? AND is_active = ?", identifier, true).
		First(&task).Error
	if err != nil {
		return nil, err
	}
	return &task, nil
}

// GetByCategoryID retrieves tasks by category ID
func (r *ActivityTaskRepository) GetByCategoryID(ctx context.Context, categoryID uint) ([]model.ActivityTask, error) {
	var tasks []model.ActivityTask
	err := r.db.WithContext(ctx).
		Preload("Category").
		Where("category_id = ? AND is_active = ?", categoryID, true).
		Order("sort_order ASC, created_at ASC").
		Find(&tasks).Error
	return tasks, err
}

// GetActive retrieves all active tasks
func (r *ActivityTaskRepository) GetActive(ctx context.Context) ([]model.ActivityTask, error) {
	var tasks []model.ActivityTask
	err := r.db.WithContext(ctx).
		Preload("Category").
		Where("is_active = ?", true).
		Order("sort_order ASC, created_at ASC").
		Find(&tasks).Error
	return tasks, err
}

// GetAvailable retrieves tasks that are currently available (active, started, not expired)
func (r *ActivityTaskRepository) GetAvailable(ctx context.Context, now time.Time) ([]model.ActivityTask, error) {
	var tasks []model.ActivityTask
	err := r.db.WithContext(ctx).
		Preload("Category").
		Where("is_active = ? AND (start_date IS NULL OR start_date <= ?) AND (end_date IS NULL OR end_date >= ?)",
			true, now, now).
		Order("sort_order ASC, created_at ASC").
		Find(&tasks).Error
	return tasks, err
}

// GetAll retrieves all tasks
func (r *ActivityTaskRepository) GetAll(ctx context.Context) ([]model.ActivityTask, error) {
	var tasks []model.ActivityTask
	err := r.db.WithContext(ctx).
		Preload("Category").
		Order("sort_order ASC, created_at ASC").
		Find(&tasks).Error
	return tasks, err
}

// GetTasksForUser retrieves available tasks for a specific user
func (r *ActivityTaskRepository) GetTasksForUser(ctx context.Context, userID uuid.UUID) ([]model.ActivityTask, error) {
	now := time.Now()
	var allTasks []model.ActivityTask

	// Get available tasks and left join with user progress to include progress info
	err := r.db.WithContext(ctx).
		Preload("Category").
		Preload("UserProgress", "user_id = ?", userID).
		Where("is_active = ? AND (start_date IS NULL OR start_date <= ?) AND (end_date IS NULL OR end_date >= ?)",
			true, now, now).
		Order("sort_order ASC, created_at ASC").
		Find(&allTasks).Error

	if err != nil {
		return nil, err
	}

	// Filter consecutive check-in tasks based on user's current progress
	filteredTasks, err := r.filterConsecutiveCheckInTasks(ctx, userID, allTasks)
	if err != nil {
		return nil, err
	}

	return filteredTasks, nil
}

// filterConsecutiveCheckInTasks filters consecutive check-in tasks based on user's current progress
// Only shows the appropriate consecutive task based on user's current streak
func (r *ActivityTaskRepository) filterConsecutiveCheckInTasks(ctx context.Context, userID uuid.UUID, tasks []model.ActivityTask) ([]model.ActivityTask, error) {
	// No special filtering needed for consecutive check-in tasks anymore
	// The configurable consecutive check-in task handles its own logic
	return tasks, nil
}

// GetDailyTasks retrieves daily tasks (by category name)
func (r *ActivityTaskRepository) GetDailyTasks(ctx context.Context) ([]model.ActivityTask, error) {
	var tasks []model.ActivityTask
	err := r.db.WithContext(ctx).
		Preload("Category").
		Joins("JOIN task_categories ON activity_tasks.category_id = task_categories.id").
		Where("task_categories.name = ? AND activity_tasks.is_active = ?", "daily", true).
		Order("activity_tasks.sort_order ASC, activity_tasks.created_at ASC").
		Find(&tasks).Error
	return tasks, err
}

// GetCommunityTasks retrieves community tasks (by category name)
func (r *ActivityTaskRepository) GetCommunityTasks(ctx context.Context) ([]model.ActivityTask, error) {
	var tasks []model.ActivityTask
	err := r.db.WithContext(ctx).
		Preload("Category").
		Joins("JOIN task_categories ON activity_tasks.category_id = task_categories.id").
		Where("task_categories.name = ? AND activity_tasks.is_active = ?", "community", true).
		Order("activity_tasks.sort_order ASC, activity_tasks.created_at ASC").
		Find(&tasks).Error
	return tasks, err
}

// GetTradingTasks retrieves trading tasks (by category name)
func (r *ActivityTaskRepository) GetTradingTasks(ctx context.Context) ([]model.ActivityTask, error) {
	var tasks []model.ActivityTask
	err := r.db.WithContext(ctx).
		Preload("Category").
		Joins("JOIN task_categories ON activity_tasks.category_id = task_categories.id").
		Where("task_categories.name = ? AND activity_tasks.is_active = ?", "trading", true).
		Order("activity_tasks.sort_order ASC, activity_tasks.created_at ASC").
		Find(&tasks).Error
	return tasks, err
}

// GetByActionTargetAndFrequency retrieves tasks by action target and frequency
func (r *ActivityTaskRepository) GetByActionTargetAndFrequency(ctx context.Context, actionTarget string, frequency model.TaskFrequency) ([]model.ActivityTask, error) {
	var tasks []model.ActivityTask
	err := r.db.WithContext(ctx).
		Preload("Category").
		Where("action_target = ? AND frequency = ? AND is_active = ?", actionTarget, frequency, true).
		Order("sort_order ASC, created_at ASC").
		Find(&tasks).Error
	return tasks, err
}
