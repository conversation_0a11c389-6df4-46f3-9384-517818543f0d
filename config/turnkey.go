package config

type Turnkey struct {
	RewardWalletSol  string `mapstructure:"reward-wallet-sol" json:"reward-wallet-sol" yaml:"reward-wallet-sol"`
	RewardWalletEvm  string `mapstructure:"reward-wallet-evm" json:"reward-wallet-evm" yaml:"reward-wallet-evm"`
	OrganizationID   string `mapstructure:"organization_id" json:"organization_id" yaml:"organization_id"`
	WalletOperatorPK string `mapstructure:"wallet_operator_pk" json:"wallet_operator_pk" yaml:"wallet_operator_pk"`
}
