# Local Development Environment
# This file contains environment variables for local development

# Application Configuration
APP_ENV=local
APP_NAME=xbit-agent
APP_VERSION=1.0.0

# Server Configuration
SERVER_HOST=127.0.0.1
SERVER_PORT=8080

# Database Configuration (PostgreSQL)
POSTGRES_AGENCY_HOST=localhost
POSTGRES_AGENCY_PORT=5433
POSTGRES_AGENCY_USER=postgres
POSTGRES_AGENCY_PASS=postgres
POSTGRES_AGENCY_SSL_MODE=disable
POSTGRES_DB=agent

# Database URL for migrations
DATABASE_URL=postgresql://postgres:postgres@127.0.0.1:5433/agent?sslmode=disable

# Redis Configuration (Optional - for caching)
REDIS_HOST=127.0.0.1
REDIS_PORT=6379
REDIS_PASS=
REDIS_DB=2

# JWT Configuration
JWT_SECRET=local-development-secret-key-not-for-production
JWT_EXPIRES_TIME=7d
JWT_BUFFER_TIME=1d
JWT_ISSUER=xbit-agent-local

# Admin Configuration
INTERNAL_API_KEY=local-internal-api-key-for-development-only

# Logging Configuration
LOG_LEVEL=debug
LOG_FORMAT=console
LOG_DIRECTOR=log
LOG_SHOW_LINE=true
LOG_IN_CONSOLE=true

# CORS Configuration (Allow all for development)
CORS_ALLOW_ORIGINS=*
CORS_ALLOW_METHODS=GET,POST,PUT,PATCH,DELETE,HEAD,OPTIONS
CORS_ALLOW_HEADERS=Origin,Content-Length,Content-Type,Authorization,X-Requested-With
CORS_ALLOW_CREDENTIALS=true

# Rate Limiting (More lenient for development)
RATE_LIMIT_COUNT=50000
RATE_LIMIT_TIME=3600

# Development Settings
DEBUG=true
ENABLE_PLAYGROUND=true

# Level downgrade configuration (disabled for first 6 months after launch)
ENABLE_LEVEL_DOWNGRADE=false

# Task seeder configuration (enabled for development)
ENABLE_TASK_SEEDER=true

# NATS Configuration (temporarily disabled - only using nats-meme for now)
# NATS_URL=nats://127.0.0.1:4222
# NATS_TOKEN=
# NATS_USE_TLS=false
# NATS_USER=
# NATS_PASS=

# NATS Configuration for Activity Cashback Processing
MEME_NATS_URL=nats://localhost:4222
MEME_NATS_USER=
MEME_NATS_PASS=
MEME_NATS_USE_TLS=false
MEME_NATS_TOKEN=

# MEME_NATS_USER=dex
# MEME_NATS_URL=nats://**********:4222,nats://**********:4222,nats://**********:4222
# MEME_NATS_PASS=zR2mIHD0JhWYDurBa0l3DQJCZo78pQJL2SZtvsfx20c0

# NATS Dex Configuration (temporarily disabled - not yet implemented for affiliate events)
# DEX_NATS_URL=
# DEX_NATS_AUTH_TOKEN=
# DEX_NATS_USE_TLS=false
# DEX_NATS_USER=
# DEX_NATS_PASS=

# Docker Compose Settings
COMPOSE_PROJECT_NAME=xbit-agent-local
