# Staging Environment Configuration
# This file contains environment variables for staging deployment

# Application Configuration
APP_ENV=staging
APP_NAME=xbit-agent
APP_VERSION=1.0.0

# Server Configuration
SERVER_HOST=0.0.0.0
SERVER_PORT=8080

# Database Configuration (PostgreSQL)
# Update these with your staging database credentials
POSTGRES_AGENCY_HOST=
POSTGRES_AGENCY_PORT=
POSTGRES_AGENCY_USER=
POSTGRES_AGENCY_PASS=
POSTGRES_AGENCY_SSL_MODE=
POSTGRES_DB=

# Database URL for migrations
DATABASE_URL=*********************************************************************************/agent?sslmode=enable

# Redis Configuration (Optional)
REDIS_HOST=
REDIS_PORT=
REDIS_PASS=
REDIS_DB=

# JWT Configuration
JWT_SECRET=staging-jwt-secret-key-change-in-production
JWT_EXPIRES_TIME=7d
JWT_BUFFER_TIME=1d
JWT_ISSUER=xbit-agent-staging

# Logging Configuration
LOG_LEVEL=info
LOG_FORMAT=console
LOG_DIRECTOR=log
LOG_SHOW_LINE=true
LOG_IN_CONSOLE=true

# CORS Configuration
# Restrict origins to your staging frontend domains
CORS_ALLOW_ORIGINS=*
CORS_ALLOW_METHODS=GET,POST,PUT,PATCH,DELETE,HEAD,OPTIONS
CORS_ALLOW_HEADERS=Origin,Content-Length,Content-Type,Authorization,X-Requested-With
CORS_ALLOW_CREDENTIALS=true

# Rate Limiting
RATE_LIMIT_COUNT=15000
RATE_LIMIT_TIME=3600

# Staging Settings
DEBUG=false
ENABLE_PLAYGROUND=true

# Level downgrade configuration (disabled for first 6 months after launch)
ENABLE_LEVEL_DOWNGRADE=false

# Task seeder configuration (disabled for staging environment)
ENABLE_TASK_SEEDER=false

SYSTEM_LAUNCH_DATE=2025-08-16